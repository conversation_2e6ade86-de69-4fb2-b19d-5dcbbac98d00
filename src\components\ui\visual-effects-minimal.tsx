'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { ScrollProgress } from './scroll-progress';

interface VisualEffectsMinimalProps {
  className?: string;
  showScrollProgress?: boolean;
  showGradients?: boolean;
}

export function VisualEffectsMinimal({
  className = '',
  showScrollProgress = true,
  showGradients = true
}: Readonly<VisualEffectsMinimalProps>) {
  return (
    <div className={cn("fixed inset-0 pointer-events-none z-0", className)}>
      {/* Scroll Progress */}
      {showScrollProgress && <ScrollProgress />}

      {/* Gradientes de fundo simples */}
      {showGradients && (
        <>
          {/* Gradiente principal */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/30 dark:from-blue-950/30 dark:to-purple-950/30" />
          
          {/* Gradientes flutuantes */}
          <motion.div
            className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-400/5 rounded-full blur-3xl"
            animate={{
              scale: [1, 1.1, 1],
              opacity: [0.3, 0.5, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />

          <motion.div
            className="absolute top-3/4 right-1/4 w-80 h-80 bg-purple-400/5 rounded-full blur-3xl"
            animate={{
              scale: [1.1, 1, 1.1],
              opacity: [0.2, 0.4, 0.2],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2,
            }}
          />

          {/* Ondas de luz */}
          <motion.div
            className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-blue-100/10 to-transparent dark:from-blue-900/10"
            animate={{
              opacity: [0.1, 0.3, 0.1],
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />

          {/* Raios de luz diagonais */}
          <motion.div
            className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-yellow-200/5 to-transparent dark:from-yellow-400/5"
            animate={{
              opacity: [0.1, 0.2, 0.1],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        </>
      )}
    </div>
  );
}
