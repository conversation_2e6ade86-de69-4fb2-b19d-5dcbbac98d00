import { createServerClient } from '@supabase/ssr';
import { NextResponse, type NextRequest } from 'next/server';
import { logger } from '@/services/LoggingService';

export async function middleware(request: NextRequest) {
  const startTime = Date.now();

  // Debug: Log da requisição
  console.log('🔍 MIDDLEWARE: Processando requisição:', request.url);
  console.log('🔍 MIDDLEWARE: Method:', request.method);
  console.log('🔍 MIDDLEWARE: Pathname:', request.nextUrl.pathname);

  // Log específico para APIs
  if (request.nextUrl.pathname.startsWith('/api/')) {
    console.log('🔍 MIDDLEWARE: Requisição para API detectada');
    console.log('🔍 MIDDLEWARE: Headers:', Object.fromEntries(request.headers.entries()));
  }

  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  // Adicionar headers de segurança
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https:",
    "connect-src 'self' https://*.supabase.co https://api.stripe.com",
    "frame-src https://js.stripe.com https://hooks.stripe.com",
  ].join('; ');
  response.headers.set('Content-Security-Policy', csp);

  // HSTS em produção
  if (process.env.NODE_ENV === 'production') {
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  }

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: any) {
          request.cookies.set({
            name,
            value,
            ...options,
          });
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          });
          response.cookies.set({
            name,
            value,
            ...options,
          });
        },
        remove(name: string, options: any) {
          request.cookies.set({
            name,
            value: '',
            ...options,
          });
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          });
          response.cookies.set({
            name,
            value: '',
            ...options,
          });
        },
      },
    }
  );

  // Obter sessão do usuário com retry em caso de falha
  let session = null;
  try {
    const { data: { session: currentSession }, error } = await supabase.auth.getSession();

    if (error) {
      console.warn('Erro ao obter sessão no middleware:', error);
      // Tentar refresh da sessão em caso de erro
      try {
        const { data: { session: refreshedSession } } = await supabase.auth.refreshSession();
        session = refreshedSession;
      } catch (refreshError) {
        console.warn('Erro ao fazer refresh da sessão:', refreshError);
      }
    } else {
      session = currentSession;
    }
  } catch (error) {
    console.error('Erro crítico ao verificar sessão:', error);
  }

  const { pathname } = request.nextUrl;

  // Rotas que requerem autenticação
  const protectedRoutes = [
    '/proprietario',
    '/colaborador',
    '/cliente',
    '/admin',
    '/onboarding',
    '/dashboard',
    '/perfil',
    '/agendamentos',
  ];

  // Rotas de autenticação (não devem ser acessadas por usuários logados)
  const authRoutes = ['/login', '/cadastro', '/recuperar-senha'];

  // Rotas públicas específicas do onboarding que não requerem autenticação
  const publicOnboardingRoutes = [
    '/onboarding/selecao-plano',
  ];

  // Verificar se é uma rota protegida
  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route));
  const isAuthRoute = authRoutes.some(route => pathname.startsWith(route));
  const isPublicOnboardingRoute = publicOnboardingRoutes.some(route => pathname.startsWith(route));

  // Se é uma rota protegida e o usuário não está autenticado
  if (isProtectedRoute && !session && !isPublicOnboardingRoute) {
    logger.warn('Unauthorized access attempt', {
      pathname,
      userAgent: request.headers.get('user-agent'),
      ip: request.headers.get('x-forwarded-for') ?? request.headers.get('x-real-ip') ?? 'unknown',
      processingTime: Date.now() - startTime
    });

    const redirectUrl = new URL('/login', request.url);
    redirectUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(redirectUrl);
  }

  // Se é uma rota de autenticação e o usuário já está logado
  if (isAuthRoute && session) {
    // Redirecionar baseado no papel do usuário
    const userRole = session.user?.user_metadata?.role;

    // Log para debug
    logger.info('Redirecionando usuário autenticado', {
      userRole,
      pathname,
      userId: session.user.id
    });

    switch (userRole) {
      case 'Administrador':
        return NextResponse.redirect(new URL('/admin/dashboard', request.url));
      case 'Proprietario':
        return NextResponse.redirect(new URL('/proprietario/dashboard-magic', request.url));
      case 'Colaborador':
        return NextResponse.redirect(new URL('/colaborador/agenda', request.url));
      default:
        return NextResponse.redirect(new URL('/cliente/dashboard', request.url));
    }
  }

  // Verificar acesso baseado em papéis para rotas específicas
  if (session && isProtectedRoute) {
    const userRole = session.user?.user_metadata?.role;
    
    // Verificar acesso a rotas de administrador
    if (pathname.startsWith('/admin') && userRole !== 'Administrador') {
      logger.warn('Access denied - insufficient privileges', {
        pathname,
        userRole,
        requiredRole: 'Administrador',
        userId: session.user.id,
        processingTime: Date.now() - startTime
      });
      return NextResponse.redirect(new URL('/acesso-negado', request.url));
    }

    // Verificar acesso a rotas de proprietário
    if (pathname.startsWith('/proprietario') && userRole !== 'Proprietario') {
      logger.warn('Access denied - insufficient privileges', {
        pathname,
        userRole,
        requiredRole: 'Proprietario',
        userId: session.user.id,
        processingTime: Date.now() - startTime
      });
      return NextResponse.redirect(new URL('/acesso-negado', request.url));
    }

    // Verificar acesso a rotas de colaborador
    if (pathname.startsWith('/colaborador') && userRole !== 'Colaborador') {
      logger.warn('Access denied - insufficient privileges', {
        pathname,
        userRole,
        requiredRole: 'Colaborador',
        userId: session.user.id,
        processingTime: Date.now() - startTime
      });
      return NextResponse.redirect(new URL('/acesso-negado', request.url));
    }
  }

  // Log de acesso bem-sucedido para rotas protegidas
  if (isProtectedRoute && session) {
    logger.info('Successful access to protected route', {
      pathname,
      userRole: session.user?.user_metadata?.role,
      userId: session.user.id,
      processingTime: Date.now() - startTime
    });
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
