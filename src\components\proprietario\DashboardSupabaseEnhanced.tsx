'use client';

import React, { memo, useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  useEmpresaProprietarioEnhanced,
  useDashboardMetricasEnhanced,
  useAgendamentosEnhanced,
  useAgendamentoMutations,
  useServicosEmpresa
} from '@/hooks/useSupabaseOptimized';
import { useSupabaseMetrics } from '@/hooks/useSupabaseEnhanced';
import { EnhancedMagicCard } from '@/components/ui/enhanced-magic-card';
import { EnhancedButton } from '@/components/ui/enhanced-button';
import { ResponsiveGrid } from '@/components/ui/enhanced-responsive';
import { TextAnimate } from '@/components/ui/text-animate';
import { NumberTicker } from '@/components/ui/number-ticker';

interface DashboardSupabaseEnhancedProps {
  empresaId: number;
  className?: string;
}

// Componente de métrica individual
const MetricCard = memo(({ 
  title, 
  value, 
  icon, 
  trend, 
  change, 
  format = 'number',
  loading = false,
  fromCache = false
}: {
  title: string;
  value: number;
  icon: string;
  trend?: 'up' | 'down' | 'stable';
  change?: number;
  format?: 'number' | 'currency' | 'percentage';
  loading?: boolean;
  fromCache?: boolean;
}) => {
  const formatValue = (val: number) => {
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL'
        }).format(val);
      case 'percentage':
        return `${val.toFixed(1)}%`;
      default:
        return val.toLocaleString('pt-BR');
    }
  };

  const getTrendConfig = () => {
    switch (trend) {
      case 'up':
        return { icon: '📈', color: 'text-green-600', bgColor: 'bg-green-100' };
      case 'down':
        return { icon: '📉', color: 'text-red-600', bgColor: 'bg-red-100' };
      default:
        return { icon: '➡️', color: 'text-gray-600', bgColor: 'bg-gray-100' };
    }
  };

  const trendConfig = getTrendConfig();

  if (loading) {
    return (
      <EnhancedMagicCard className="h-full animate-pulse">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="w-8 h-8 bg-gray-200 rounded"></div>
            <div className="w-16 h-6 bg-gray-200 rounded"></div>
          </div>
          <div className="space-y-2">
            <div className="w-20 h-8 bg-gray-200 rounded"></div>
            <div className="w-32 h-4 bg-gray-200 rounded"></div>
          </div>
        </div>
      </EnhancedMagicCard>
    );
  }

  return (
    <EnhancedMagicCard 
      className="h-full"
      ariaLabel={`Métrica: ${title}`}
      focusable
    >
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <motion.div
            className="text-2xl"
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ duration: 0.2 }}
          >
            {icon}
          </motion.div>
          
          <div className="flex items-center space-x-2">
            {change !== undefined && (
              <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${trendConfig.bgColor} ${trendConfig.color}`}>
                <span>{trendConfig.icon}</span>
                <span>{change > 0 ? '+' : ''}{change}%</span>
              </div>
            )}
            
            {fromCache && (
              <div className="w-2 h-2 bg-blue-500 rounded-full" title="Dados do cache" />
            )}
          </div>
        </div>

        {/* Valor */}
        <div className="space-y-2">
          <div className="text-3xl font-bold text-gray-900 dark:text-white">
            {format === 'number' ? (
              <NumberTicker value={value} />
            ) : (
              formatValue(value)
            )}
          </div>
          
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {title}
          </div>
        </div>
      </div>
    </EnhancedMagicCard>
  );
});

MetricCard.displayName = 'MetricCard';

// Componente principal
const DashboardSupabaseEnhanced = memo(({ 
  empresaId, 
  className = '' 
}: DashboardSupabaseEnhancedProps) => {
  const [realtimeEnabled, setRealtimeEnabled] = useState(false);

  // Hooks de dados
  const { 
    empresa, 
    loading: loadingEmpresa, 
    error: errorEmpresa 
  } = useEmpresaProprietarioEnhanced(empresaId);

  const {
    metricas,
    loading: loadingMetricas,
    error: errorMetricas,
    fromCache: metricasFromCache,
    totalAgendamentos,
    receitaMensal,
    clientesAtivos,
    taxaConfirmacao
  } = useDashboardMetricasEnhanced(empresaId);

  const {
    agendamentos,
    loading: loadingAgendamentos,
    error: errorAgendamentos
  } = useAgendamentosEnhanced(empresaId, {
    status: ['pendente', 'confirmado'],
    limit: 10,
    realtime: realtimeEnabled
  });

  const {
    servicos,
    loading: loadingServicos
  } = useServicosEmpresa(empresaId);

  const {
    criarAgendamento,
    loading: mutationLoading
  } = useAgendamentoMutations(empresaId);

  // Métricas do Supabase
  const supabaseMetrics = useSupabaseMetrics();

  const handleCreateTestAgendamento = useCallback(async () => {
    try {
      await criarAgendamento({
        servico_id: servicos[0]?.servico_id,
        data_agendamento: new Date().toISOString().split('T')[0],
        hora_inicio: '10:00',
        hora_fim: '11:00',
        status: 'pendente',
        valor_total: 100,
        tipo_agendamento: 'servico'
      });
    } catch (error) {
      console.error('Erro ao criar agendamento:', error);
    }
  }, [criarAgendamento, servicos]);

  if (loadingEmpresa) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity }}
            className="text-4xl"
          >
            ⟳
          </motion.div>
          <p className="text-gray-600">Carregando dados da empresa...</p>
        </div>
      </div>
    );
  }

  if (errorEmpresa) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <EnhancedMagicCard className="p-8 text-center">
          <div className="text-red-600 text-4xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Erro ao carregar empresa
          </h2>
          <p className="text-gray-600">{errorEmpresa}</p>
        </EnhancedMagicCard>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-8 ${className}`}>
      {/* Header */}
      <motion.div
        className="mb-8"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="flex items-center justify-between">
          <div>
            <TextAnimate
              animation="blurInUp"
              className="text-4xl font-bold text-gray-900 dark:text-white mb-2"
            >
              🚀 Dashboard Supabase Enhanced
            </TextAnimate>
            <p className="text-lg text-gray-600 dark:text-gray-400">
              {empresa?.nome_empresa} • Integração otimizada com cache inteligente
            </p>
          </div>

          <div className="flex items-center space-x-4">
            <EnhancedButton
              variant={realtimeEnabled ? 'success' : 'secondary'}
              onClick={() => setRealtimeEnabled(!realtimeEnabled)}
              leftIcon={realtimeEnabled ? '🟢' : '⚪'}
            >
              Realtime {realtimeEnabled ? 'ON' : 'OFF'}
            </EnhancedButton>

            <EnhancedButton
              variant="primary"
              onClick={handleCreateTestAgendamento}
              loading={mutationLoading}
              disabled={!servicos.length}
            >
              Criar Agendamento Teste
            </EnhancedButton>
          </div>
        </div>
      </motion.div>

      {/* Métricas Principais */}
      <motion.section
        className="mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.6 }}
      >
        <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
          📊 Métricas de Negócio
        </h2>

        <ResponsiveGrid
          columns={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4, '2xl': 4 }}
          gap={{ xs: 4, sm: 4, md: 6, lg: 6, xl: 8, '2xl': 8 }}
        >
          <MetricCard
            title="Total Agendamentos"
            value={totalAgendamentos}
            icon="📅"
            trend="up"
            change={12.5}
            loading={loadingMetricas}
            fromCache={metricasFromCache}
          />
          
          <MetricCard
            title="Receita Mensal"
            value={receitaMensal}
            icon="💰"
            trend="up"
            change={8.3}
            format="currency"
            loading={loadingMetricas}
            fromCache={metricasFromCache}
          />
          
          <MetricCard
            title="Clientes Ativos"
            value={clientesAtivos}
            icon="👥"
            trend="stable"
            loading={loadingMetricas}
            fromCache={metricasFromCache}
          />
          
          <MetricCard
            title="Taxa Confirmação"
            value={taxaConfirmacao}
            icon="✅"
            trend="up"
            change={3.2}
            format="percentage"
            loading={loadingMetricas}
            fromCache={metricasFromCache}
          />
        </ResponsiveGrid>
      </motion.section>

      {/* Agendamentos Recentes */}
      <motion.section
        className="mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4, duration: 0.6 }}
      >
        <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
          📋 Agendamentos Recentes {realtimeEnabled && '(Tempo Real)'}
        </h2>

        <EnhancedMagicCard className="p-6">
          {loadingAgendamentos ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse flex space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : agendamentos.length > 0 ? (
            <div className="space-y-4">
              {agendamentos.map((agendamento, index) => (
                <motion.div
                  key={agendamento.agendamento_id}
                  className="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className="text-2xl">
                    {agendamento.status === 'confirmado' ? '✅' : '⏳'}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-white">
                      Agendamento #{agendamento.agendamento_id}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {agendamento.data_agendamento} às {agendamento.hora_inicio} • 
                      Status: {agendamento.status}
                    </div>
                  </div>
                  <div className="text-lg font-semibold text-green-600">
                    {new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL'
                    }).format(agendamento.valor_total)}
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-4xl mb-4">📅</div>
              <p className="text-gray-600 dark:text-gray-400">
                Nenhum agendamento encontrado
              </p>
            </div>
          )}
        </EnhancedMagicCard>
      </motion.section>

      {/* Métricas do Supabase */}
      {supabaseMetrics && (
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.6 }}
        >
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
            ⚡ Performance Supabase
          </h2>

          <ResponsiveGrid
            columns={{ xs: 1, sm: 2, md: 3, lg: 5, xl: 5, '2xl': 5 }}
            gap={{ xs: 4, sm: 4, md: 6, lg: 6, xl: 8, '2xl': 8 }}
          >
            <MetricCard
              title="Total Queries"
              value={supabaseMetrics.totalQueries}
              icon="🔍"
            />
            
            <MetricCard
              title="Queries Sucesso"
              value={supabaseMetrics.successfulQueries}
              icon="✅"
            />
            
            <MetricCard
              title="Cache Hit Rate"
              value={supabaseMetrics.cacheHitRate}
              icon="⚡"
              format="percentage"
            />
            
            <MetricCard
              title="Tempo Médio (ms)"
              value={Math.round(supabaseMetrics.averageResponseTime)}
              icon="⏱️"
            />
            
            <MetricCard
              title="Queries Falha"
              value={supabaseMetrics.failedQueries}
              icon="❌"
              trend={supabaseMetrics.failedQueries > 0 ? 'down' : 'stable'}
            />
          </ResponsiveGrid>
        </motion.section>
      )}
    </div>
  );
});

DashboardSupabaseEnhanced.displayName = 'DashboardSupabaseEnhanced';

export { DashboardSupabaseEnhanced };
