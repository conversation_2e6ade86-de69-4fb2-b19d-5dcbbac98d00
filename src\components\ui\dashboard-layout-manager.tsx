'use client';

import React, { memo, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MagicCard } from './magic-card';
import { ShimmerButton } from './shimmer-button';
import { TextAnimate } from './text-animate';
import { AdvancedSection, SectionAction, SectionMetric } from './dashboard-sections-advanced';
import { 
  useDashboardConfig, 
  DASHBOARD_SECTIONS, 
  SECTION_LAYOUTS,
  DashboardConfigManager 
} from '@/config/dashboard-sections';

interface DashboardLayoutManagerProps {
  children: Record<string, React.ReactNode>;
  className?: string;
  onSectionRefresh?: (sectionId: string) => Promise<void>;
  sectionMetrics?: Record<string, SectionMetric[]>;
  sectionActions?: Record<string, SectionAction[]>;
  sectionLoading?: Record<string, boolean>;
  sectionErrors?: Record<string, string>;
  lastUpdated?: Record<string, Date>;
}

interface LayoutCustomizerProps {
  isOpen: boolean;
  onClose: () => void;
  preferences: any;
  onUpdatePreferences: (updates: any) => void;
}

// Componente para customização do layout
const LayoutCustomizer = memo(({
  isOpen,
  onClose,
  preferences,
  onUpdatePreferences
}: Readonly<LayoutCustomizerProps>) => {
  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 flex items-center justify-center p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        {/* Backdrop */}
        <motion.div
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        />

        {/* Panel */}
        <motion.div
          className="relative z-10 w-full max-w-4xl max-h-[90vh] overflow-y-auto"
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
        >
          <MagicCard className="p-6" gradientColor="#6366F1">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <TextAnimate
                animation="blurInUp"
                className="text-2xl font-bold text-gray-900 dark:text-white"
              >
                🎛️ Personalizar Dashboard
              </TextAnimate>
              
              <button
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 text-2xl"
                aria-label="Fechar customizador"
              >
                ✕
              </button>
            </div>

            {/* Layouts disponíveis */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                📐 Layouts Disponíveis
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(SECTION_LAYOUTS).map(([layoutKey, layoutConfig]) => (
                  <motion.button
                    key={layoutKey}
                    onClick={() => onUpdatePreferences({ layout: layoutKey })}
                    className={`p-4 rounded-lg border text-left transition-all ${
                      preferences.layout === layoutKey
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="font-medium text-gray-900 dark:text-white capitalize">
                      {layoutKey.replace(/([A-Z])/g, ' $1')}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {layoutConfig.length} seções configuradas
                    </div>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Seções disponíveis */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                📋 Gerenciar Seções
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(DASHBOARD_SECTIONS).map(([sectionId, config]) => {
                  const isHidden = preferences.hiddenSections.includes(sectionId);
                  
                  return (
                    <motion.div
                      key={sectionId}
                      className={`p-4 rounded-lg border ${
                        isHidden 
                          ? 'border-gray-200 bg-gray-50 dark:bg-gray-800 opacity-60'
                          : 'border-gray-300 bg-white dark:bg-gray-700'
                      }`}
                      layout
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <span className="text-xl">{config.icon}</span>
                          <div>
                            <div className="font-medium text-gray-900 dark:text-white">
                              {config.title}
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-400">
                              {config.category} • {config.priority}
                            </div>
                          </div>
                        </div>
                        
                        <motion.button
                          onClick={() => {
                            const hiddenSections = isHidden
                              ? preferences.hiddenSections.filter((id: string) => id !== sectionId)
                              : [...preferences.hiddenSections, sectionId];
                            onUpdatePreferences({ hiddenSections });
                          }}
                          className={`px-3 py-1 text-xs rounded-full transition-colors ${
                            isHidden
                              ? 'bg-green-100 text-green-700 hover:bg-green-200'
                              : 'bg-red-100 text-red-700 hover:bg-red-200'
                          }`}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          {isHidden ? '👁️ Mostrar' : '🙈 Ocultar'}
                        </motion.button>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </div>

            {/* Ações */}
            <div className="flex space-x-3">
              <ShimmerButton
                onClick={() => {
                  onUpdatePreferences({
                    layout: 'default',
                    hiddenSections: [],
                    collapsedSections: []
                  });
                }}
                className="flex-1 text-sm py-3"
                shimmerColor="#EF4444"
                background="#EF444420"
              >
                🔄 Resetar para Padrão
              </ShimmerButton>
              
              <ShimmerButton
                onClick={onClose}
                className="flex-1 text-sm py-3"
                shimmerColor="#10B981"
                background="#10B98120"
              >
                ✅ Salvar e Fechar
              </ShimmerButton>
            </div>
          </MagicCard>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
});

LayoutCustomizer.displayName = 'LayoutCustomizer';

// Componente principal do gerenciador de layout
const DashboardLayoutManager = memo(({
  children,
  className = '',
  onSectionRefresh,
  sectionMetrics = {},
  sectionActions = {},
  sectionLoading = {},
  sectionErrors = {},
  lastUpdated = {}
}: Readonly<DashboardLayoutManagerProps>) => {
  const {
    preferences,
    updatePreferences,
    getVisibleSections,
    getSectionConfig
  } = useDashboardConfig();

  const [showCustomizer, setShowCustomizer] = useState(false);
  const [refreshingSection, setRefreshingSection] = useState<string | null>(null);

  const handleSectionRefresh = useCallback(async (sectionId: string) => {
    if (!onSectionRefresh || refreshingSection) return;
    
    setRefreshingSection(sectionId);
    try {
      await onSectionRefresh(sectionId);
    } finally {
      setRefreshingSection(null);
    }
  }, [onSectionRefresh, refreshingSection]);

  const renderSection = useCallback((sectionId: string) => {
    const config = getSectionConfig(sectionId);
    if (!config || !children[sectionId]) return null;

    const isCollapsed = preferences.collapsedSections.includes(sectionId);
    const sectionActionsWithRefresh = [
      ...(sectionActions[sectionId] || []),
      ...(onSectionRefresh ? [{
        id: 'refresh',
        label: 'Atualizar',
        icon: '🔄',
        variant: 'secondary' as const,
        onClick: () => handleSectionRefresh(sectionId),
        loading: refreshingSection === sectionId
      }] : [])
    ];

    return (
      <AdvancedSection
        key={sectionId}
        config={{
          ...config,
          defaultExpanded: !isCollapsed
        }}
        actions={sectionActionsWithRefresh}
        metrics={sectionMetrics[sectionId]}
        loading={sectionLoading[sectionId]}
        error={sectionErrors[sectionId]}
        lastUpdated={lastUpdated[sectionId]}
        onRefresh={() => handleSectionRefresh(sectionId)}
      >
        {children[sectionId]}
      </AdvancedSection>
    );
  }, [
    getSectionConfig,
    children,
    preferences.collapsedSections,
    sectionActions,
    sectionMetrics,
    sectionLoading,
    sectionErrors,
    lastUpdated,
    onSectionRefresh,
    handleSectionRefresh,
    refreshingSection
  ]);

  const visibleSections = getVisibleSections();
  const layoutConfig = SECTION_LAYOUTS[preferences.layout];

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header com controles */}
      <div className="flex items-center justify-between">
        <div>
          <TextAnimate
            animation="blurInUp"
            className="text-3xl font-bold text-gray-900 dark:text-white"
          >
            Dashboard ServiceTech
          </TextAnimate>
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-gray-600 dark:text-gray-400 mt-2"
          >
            Visão geral completa do seu negócio • Layout: {preferences.layout}
          </motion.p>
        </div>

        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          <ShimmerButton
            onClick={() => setShowCustomizer(true)}
            className="px-4 py-2"
            shimmerColor="#6366F1"
            background="#6366F120"
          >
            🎛️ Personalizar
          </ShimmerButton>
        </motion.div>
      </div>

      {/* Renderizar seções baseado no layout */}
      <div className="space-y-8">
        {layoutConfig.map((item, index) => {
          if (typeof item === 'string') {
            // Seção individual
            if (!visibleSections.includes(item)) return null;
            return (
              <motion.div
                key={item}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                {renderSection(item)}
              </motion.div>
            );
          } else if (item.type === 'grid') {
            // Grid de seções
            const gridSections = item.sections.filter(sectionId => 
              visibleSections.includes(sectionId)
            );
            
            if (gridSections.length === 0) return null;

            const gridCols = {
              1: 'grid-cols-1',
              2: 'grid-cols-1 lg:grid-cols-2',
              3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
              4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
            };

            return (
              <motion.div
                key={`grid-${index}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`grid ${gridCols[item.columns as keyof typeof gridCols]} gap-8`}
              >
                {gridSections.map(sectionId => renderSection(sectionId))}
              </motion.div>
            );
          }
          
          return null;
        })}
      </div>

      {/* Customizador de layout */}
      <LayoutCustomizer
        isOpen={showCustomizer}
        onClose={() => setShowCustomizer(false)}
        preferences={preferences}
        onUpdatePreferences={updatePreferences}
      />
    </div>
  );
});

DashboardLayoutManager.displayName = 'DashboardLayoutManager';

export { DashboardLayoutManager };
