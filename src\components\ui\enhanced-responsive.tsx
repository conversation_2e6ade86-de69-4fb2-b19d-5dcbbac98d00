'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { motion } from 'framer-motion';

// Tipos para configurações responsivas avançadas
interface ResponsiveBreakpoints {
  xs: number;    // 0px
  sm: number;    // 640px
  md: number;    // 768px
  lg: number;    // 1024px
  xl: number;    // 1280px
  '2xl': number; // 1536px
}

interface DeviceCapabilities {
  hasTouch: boolean;
  hasHover: boolean;
  hasPointer: boolean;
  supportsWebGL: boolean;
  supportsIntersectionObserver: boolean;
  supportsResizeObserver: boolean;
  connectionType: 'slow-2g' | '2g' | '3g' | '4g' | 'unknown';
  deviceMemory: number;
  hardwareConcurrency: number;
}

interface ResponsiveState {
  width: number;
  height: number;
  breakpoint: keyof ResponsiveBreakpoints;
  orientation: 'portrait' | 'landscape';
  pixelRatio: number;
  isTouch: boolean;
  isRetina: boolean;
  capabilities: DeviceCapabilities;
  prefersReducedMotion: boolean;
  prefersColorScheme: 'light' | 'dark' | 'no-preference';
  prefersContrast: 'no-preference' | 'more' | 'less';
}

// Breakpoints padrão
const DEFAULT_BREAKPOINTS: ResponsiveBreakpoints = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
};

// Context para responsividade
const ResponsiveContext = createContext<ResponsiveState | null>(null);

// Provider de responsividade avançada
interface ResponsiveProviderProps {
  children: React.ReactNode;
  breakpoints?: Partial<ResponsiveBreakpoints>;
}

export const ResponsiveProvider: React.FC<ResponsiveProviderProps> = ({
  children,
  breakpoints = {}
}) => {
  const finalBreakpoints = { ...DEFAULT_BREAKPOINTS, ...breakpoints };
  
  const [state, setState] = useState<ResponsiveState>(() => {
    if (typeof window === 'undefined') {
      return {
        width: 1024,
        height: 768,
        breakpoint: 'lg',
        orientation: 'landscape',
        pixelRatio: 1,
        isTouch: false,
        isRetina: false,
        capabilities: {
          hasTouch: false,
          hasHover: true,
          hasPointer: true,
          supportsWebGL: false,
          supportsIntersectionObserver: false,
          supportsResizeObserver: false,
          connectionType: 'unknown',
          deviceMemory: 4,
          hardwareConcurrency: 4
        },
        prefersReducedMotion: false,
        prefersColorScheme: 'light',
        prefersContrast: 'no-preference'
      };
    }

    return getResponsiveState(finalBreakpoints);
  });

  const updateState = useCallback(() => {
    setState(getResponsiveState(finalBreakpoints));
  }, [finalBreakpoints]);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Listeners para mudanças
    window.addEventListener('resize', updateState);
    window.addEventListener('orientationchange', updateState);

    // Media queries para preferências
    const motionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const colorQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const contrastQuery = window.matchMedia('(prefers-contrast: more)');

    const handleMotionChange = () => updateState();
    const handleColorChange = () => updateState();
    const handleContrastChange = () => updateState();

    motionQuery.addEventListener('change', handleMotionChange);
    colorQuery.addEventListener('change', handleColorChange);
    contrastQuery.addEventListener('change', handleContrastChange);

    return () => {
      window.removeEventListener('resize', updateState);
      window.removeEventListener('orientationchange', updateState);
      motionQuery.removeEventListener('change', handleMotionChange);
      colorQuery.removeEventListener('change', handleColorChange);
      contrastQuery.removeEventListener('change', handleContrastChange);
    };
  }, [updateState]);

  return (
    <ResponsiveContext.Provider value={state}>
      {children}
    </ResponsiveContext.Provider>
  );
};

// Hook para usar responsividade
export const useResponsive = () => {
  const context = useContext(ResponsiveContext);
  if (!context) {
    throw new Error('useResponsive must be used within ResponsiveProvider');
  }
  return context;
};

// Função para obter estado responsivo
function getResponsiveState(breakpoints: ResponsiveBreakpoints): ResponsiveState {
  const width = window.innerWidth;
  const height = window.innerHeight;
  
  // Determinar breakpoint atual
  let breakpoint: keyof ResponsiveBreakpoints = 'xs';
  if (width >= breakpoints['2xl']) breakpoint = '2xl';
  else if (width >= breakpoints.xl) breakpoint = 'xl';
  else if (width >= breakpoints.lg) breakpoint = 'lg';
  else if (width >= breakpoints.md) breakpoint = 'md';
  else if (width >= breakpoints.sm) breakpoint = 'sm';

  // Detectar capacidades do dispositivo
  const capabilities: DeviceCapabilities = {
    hasTouch: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
    hasHover: window.matchMedia('(hover: hover)').matches,
    hasPointer: window.matchMedia('(pointer: fine)').matches,
    supportsWebGL: !!window.WebGLRenderingContext,
    supportsIntersectionObserver: 'IntersectionObserver' in window,
    supportsResizeObserver: 'ResizeObserver' in window,
    connectionType: getConnectionType(),
    deviceMemory: (navigator as any).deviceMemory || 4,
    hardwareConcurrency: navigator.hardwareConcurrency || 4
  };

  return {
    width,
    height,
    breakpoint,
    orientation: width > height ? 'landscape' : 'portrait',
    pixelRatio: window.devicePixelRatio || 1,
    isTouch: capabilities.hasTouch,
    isRetina: window.devicePixelRatio > 1,
    capabilities,
    prefersReducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
    prefersColorScheme: window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light',
    prefersContrast: window.matchMedia('(prefers-contrast: more)').matches ? 'more' : 'no-preference'
  };
}

// Função para detectar tipo de conexão
function getConnectionType(): DeviceCapabilities['connectionType'] {
  const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
  if (!connection) return 'unknown';
  
  const effectiveType = connection.effectiveType;
  if (['slow-2g', '2g', '3g', '4g'].includes(effectiveType)) {
    return effectiveType;
  }
  
  return 'unknown';
}

// Hook para breakpoints específicos
export const useBreakpoint = (breakpoint: keyof ResponsiveBreakpoints) => {
  const { breakpoint: current } = useResponsive();
  const breakpointOrder: (keyof ResponsiveBreakpoints)[] = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];
  
  const currentIndex = breakpointOrder.indexOf(current);
  const targetIndex = breakpointOrder.indexOf(breakpoint);
  
  return {
    isExact: current === breakpoint,
    isAbove: currentIndex > targetIndex,
    isBelow: currentIndex < targetIndex,
    isAtLeast: currentIndex >= targetIndex,
    isAtMost: currentIndex <= targetIndex
  };
};

// Hook para media queries customizadas
export const useMediaQuery = (query: string) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia(query);
    setMatches(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setMatches(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [query]);

  return matches;
};

// Componente para renderização condicional baseada em breakpoint
interface ResponsiveProps {
  children: React.ReactNode;
  breakpoint?: keyof ResponsiveBreakpoints;
  above?: keyof ResponsiveBreakpoints;
  below?: keyof ResponsiveBreakpoints;
  only?: keyof ResponsiveBreakpoints;
  fallback?: React.ReactNode;
}

export const Responsive: React.FC<ResponsiveProps> = ({
  children,
  breakpoint,
  above,
  below,
  only,
  fallback = null
}) => {
  const responsive = useResponsive();
  
  let shouldRender = true;

  if (only) {
    shouldRender = responsive.breakpoint === only;
  } else {
    if (breakpoint) {
      const bp = useBreakpoint(breakpoint);
      shouldRender = bp.isAtLeast;
    }
    
    if (above) {
      const bp = useBreakpoint(above);
      shouldRender = shouldRender && bp.isAbove;
    }
    
    if (below) {
      const bp = useBreakpoint(below);
      shouldRender = shouldRender && bp.isBelow;
    }
  }

  return shouldRender ? <>{children}</> : <>{fallback}</>;
};

// Componente para container responsivo
interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: keyof ResponsiveBreakpoints | 'none';
  padding?: boolean;
}

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  className = '',
  maxWidth = '2xl',
  padding = true
}) => {
  const { breakpoint } = useResponsive();
  
  const getMaxWidthClass = () => {
    if (maxWidth === 'none') return '';
    
    const maxWidths = {
      xs: 'max-w-none',
      sm: 'max-w-sm',
      md: 'max-w-md',
      lg: 'max-w-lg',
      xl: 'max-w-xl',
      '2xl': 'max-w-2xl'
    };
    
    return maxWidths[maxWidth] || 'max-w-2xl';
  };

  const getPaddingClass = () => {
    if (!padding) return '';
    
    const paddings = {
      xs: 'px-4',
      sm: 'px-4',
      md: 'px-6',
      lg: 'px-8',
      xl: 'px-8',
      '2xl': 'px-8'
    };
    
    return paddings[breakpoint] || 'px-4';
  };

  return (
    <div className={`mx-auto ${getMaxWidthClass()} ${getPaddingClass()} ${className}`}>
      {children}
    </div>
  );
};

// Hook para otimizações baseadas em performance
export const usePerformanceOptimizations = () => {
  const { capabilities, prefersReducedMotion, isTouch } = useResponsive();

  return {
    // Reduzir animações em dispositivos lentos
    shouldReduceAnimations: prefersReducedMotion ||
                           capabilities.connectionType === 'slow-2g' ||
                           capabilities.connectionType === '2g' ||
                           capabilities.deviceMemory < 2,

    // Reduzir efeitos visuais em dispositivos com pouca memória
    shouldReduceEffects: capabilities.deviceMemory < 4 ||
                        capabilities.hardwareConcurrency < 4,

    // Usar lazy loading mais agressivo em conexões lentas
    shouldUseLazyLoading: capabilities.connectionType === 'slow-2g' ||
                         capabilities.connectionType === '2g',

    // Otimizar para touch
    shouldOptimizeForTouch: isTouch,

    // Usar WebGL se disponível
    canUseWebGL: capabilities.supportsWebGL,

    // Usar observers se disponíveis
    canUseIntersectionObserver: capabilities.supportsIntersectionObserver,
    canUseResizeObserver: capabilities.supportsResizeObserver
  };
};

// Componente de grid responsivo avançado
interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  columns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };
  gap?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };
  autoRows?: string;
  autoFit?: boolean;
  minItemWidth?: string;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  className = '',
  columns = { xs: 1, sm: 2, md: 3, lg: 4, xl: 5, '2xl': 6 },
  gap = { xs: 4, sm: 4, md: 6, lg: 6, xl: 8, '2xl': 8 },
  autoRows = 'auto',
  autoFit = false,
  minItemWidth = '250px'
}) => {
  const { breakpoint } = useResponsive();

  const currentColumns = columns[breakpoint] || columns.md || 3;
  const currentGap = gap[breakpoint] || gap.md || 6;

  const gridStyle = autoFit ? {
    display: 'grid',
    gridTemplateColumns: `repeat(auto-fit, minmax(${minItemWidth}, 1fr))`,
    gap: `${currentGap * 0.25}rem`,
    gridAutoRows: autoRows
  } : {
    display: 'grid',
    gridTemplateColumns: `repeat(${currentColumns}, 1fr)`,
    gap: `${currentGap * 0.25}rem`,
    gridAutoRows: autoRows
  };

  return (
    <div
      className={`responsive-grid ${className}`}
      style={gridStyle}
    >
      {children}
    </div>
  );
};

// Hook para detecção de orientação
export const useOrientation = () => {
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');

  useEffect(() => {
    const updateOrientation = () => {
      setOrientation(window.innerHeight > window.innerWidth ? 'portrait' : 'landscape');
    };

    updateOrientation();
    window.addEventListener('orientationchange', updateOrientation);
    window.addEventListener('resize', updateOrientation);

    return () => {
      window.removeEventListener('orientationchange', updateOrientation);
      window.removeEventListener('resize', updateOrientation);
    };
  }, []);

  return orientation;
};

// Hook para detecção de scroll
export const useScrollPosition = () => {
  const [scrollPosition, setScrollPosition] = useState({ x: 0, y: 0 });
  const [scrollDirection, setScrollDirection] = useState<'up' | 'down' | 'left' | 'right' | null>(null);

  useEffect(() => {
    let lastScrollY = window.scrollY;
    let lastScrollX = window.scrollX;

    const updateScrollPosition = () => {
      const currentScrollY = window.scrollY;
      const currentScrollX = window.scrollX;

      // Determinar direção
      if (currentScrollY > lastScrollY) {
        setScrollDirection('down');
      } else if (currentScrollY < lastScrollY) {
        setScrollDirection('up');
      } else if (currentScrollX > lastScrollX) {
        setScrollDirection('right');
      } else if (currentScrollX < lastScrollX) {
        setScrollDirection('left');
      }

      setScrollPosition({ x: currentScrollX, y: currentScrollY });
      lastScrollY = currentScrollY;
      lastScrollX = currentScrollX;
    };

    window.addEventListener('scroll', updateScrollPosition, { passive: true });
    return () => window.removeEventListener('scroll', updateScrollPosition);
  }, []);

  return { ...scrollPosition, direction: scrollDirection };
};
