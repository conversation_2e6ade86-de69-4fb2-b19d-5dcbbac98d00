'use client';

import React, { memo, useState, useCallback } from 'react';
import { DashboardLayoutManager } from '@/components/ui/dashboard-layout-manager';
import { SectionAction, SectionMetric } from '@/components/ui/dashboard-sections-advanced';
import { InformacoesEmpresaOrganizada } from './sections/InformacoesEmpresaOrganizada';
import { MetricasNegocioMagic } from './MetricasNegocioMagic';
import { StatusPlanoSaasMagic } from './StatusPlanoSaasMagic';
import { AlertasDashboardMagic } from './AlertasDashboardMagic';
import { AcoesRapidasMagic } from './AcoesRapidasMagic';
import { motion } from 'framer-motion';
import { TextAnimate } from '@/components/ui/text-animate';
import { ShimmerButton } from '@/components/ui/shimmer-button';

interface DashboardSecoesOrganizadasProps {
  className?: string;
}

const DashboardSecoesOrganizadas = memo(({
  className = ''
}: Readonly<DashboardSecoesOrganizadasProps>) => {
  const [refreshingSection, setRefreshingSection] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Record<string, Date>>({});

  // Configurar ações específicas para cada seção
  const sectionActions: Record<string, SectionAction[]> = {
    'informacoes-empresa': [
      {
        id: 'edit-empresa',
        label: 'Editar Dados',
        icon: '✏️',
        variant: 'primary',
        onClick: () => {
          console.log('Editando dados da empresa...');
          // Aqui você implementaria a lógica de edição
        }
      },
      {
        id: 'backup-empresa',
        label: 'Backup',
        icon: '💾',
        variant: 'secondary',
        onClick: () => {
          console.log('Fazendo backup dos dados...');
        }
      }
    ],
    'metricas-negocio': [
      {
        id: 'export-metrics',
        label: 'Exportar',
        icon: '📊',
        variant: 'primary',
        onClick: () => {
          console.log('Exportando métricas...');
        }
      },
      {
        id: 'detailed-report',
        label: 'Relatório Detalhado',
        icon: '📈',
        variant: 'success',
        onClick: () => {
          console.log('Gerando relatório detalhado...');
        }
      },
      {
        id: 'configure-alerts',
        label: 'Configurar Alertas',
        icon: '🔔',
        variant: 'secondary',
        onClick: () => {
          console.log('Configurando alertas de métricas...');
        }
      }
    ],
    'status-plano-saas': [
      {
        id: 'upgrade-plan',
        label: 'Fazer Upgrade',
        icon: '⬆️',
        variant: 'success',
        onClick: () => {
          console.log('Iniciando processo de upgrade...');
        },
        badge: 'Novo'
      },
      {
        id: 'billing-history',
        label: 'Histórico',
        icon: '📋',
        variant: 'secondary',
        onClick: () => {
          console.log('Visualizando histórico de cobrança...');
        }
      }
    ],
    'alertas-dashboard': [
      {
        id: 'mark-all-read',
        label: 'Marcar Todos Lidos',
        icon: '✅',
        variant: 'secondary',
        onClick: () => {
          console.log('Marcando todos os alertas como lidos...');
        }
      },
      {
        id: 'configure-notifications',
        label: 'Configurar',
        icon: '⚙️',
        variant: 'primary',
        onClick: () => {
          console.log('Configurando notificações...');
        }
      }
    ],
    'acoes-rapidas': [
      {
        id: 'customize-actions',
        label: 'Personalizar',
        icon: '🎨',
        variant: 'primary',
        onClick: () => {
          console.log('Personalizando ações rápidas...');
        }
      },
      {
        id: 'add-shortcut',
        label: 'Adicionar Atalho',
        icon: '➕',
        variant: 'success',
        onClick: () => {
          console.log('Adicionando novo atalho...');
        }
      }
    ]
  };

  // Métricas de exemplo para as seções
  const sectionMetrics: Record<string, SectionMetric[]> = {
    'informacoes-empresa': [
      {
        label: 'Configuração',
        value: 85,
        change: 5,
        trend: 'up',
        format: 'percentage',
        icon: '⚙️'
      },
      {
        label: 'Última Atualização',
        value: '2 dias atrás',
        icon: '🔄'
      }
    ],
    'metricas-negocio': [
      {
        label: 'Receita Mensal',
        value: 15750,
        change: 12.5,
        trend: 'up',
        format: 'currency',
        icon: '💰'
      },
      {
        label: 'Agendamentos',
        value: 234,
        change: 8.3,
        trend: 'up',
        format: 'number',
        icon: '📅'
      },
      {
        label: 'Taxa de Conversão',
        value: 68.5,
        change: -2.1,
        trend: 'down',
        format: 'percentage',
        icon: '🎯'
      },
      {
        label: 'Satisfação',
        value: 94.2,
        change: 1.8,
        trend: 'up',
        format: 'percentage',
        icon: '⭐'
      }
    ],
    'status-plano-saas': [
      {
        label: 'Uso do Plano',
        value: 67,
        change: 15,
        trend: 'up',
        format: 'percentage',
        icon: '📊'
      },
      {
        label: 'Dias Restantes',
        value: 23,
        icon: '📅'
      }
    ],
    'alertas-dashboard': [
      {
        label: 'Alertas Ativos',
        value: 3,
        icon: '🔔'
      },
      {
        label: 'Críticos',
        value: 1,
        icon: '🚨'
      }
    ],
    'acoes-rapidas': [
      {
        label: 'Ações Hoje',
        value: 12,
        change: 25,
        trend: 'up',
        icon: '⚡'
      },
      {
        label: 'Mais Usada',
        value: 'Novo Agendamento',
        icon: '🏆'
      }
    ]
  };

  // Estados de loading e erro simulados
  const [sectionLoading, setSectionLoading] = useState<Record<string, boolean>>({});
  const [sectionErrors, setSectionErrors] = useState<Record<string, string>>({});

  // Função para refresh das seções
  const handleSectionRefresh = useCallback(async (sectionId: string) => {
    setRefreshingSection(sectionId);
    setSectionLoading(prev => ({ ...prev, [sectionId]: true }));
    setSectionErrors(prev => ({ ...prev, [sectionId]: '' }));

    try {
      // Simular chamada de API
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simular possível erro (10% de chance)
      if (Math.random() < 0.1) {
        throw new Error('Erro simulado na atualização');
      }

      // Atualizar timestamp
      setLastUpdated(prev => ({
        ...prev,
        [sectionId]: new Date()
      }));

      console.log(`Seção ${sectionId} atualizada com sucesso!`);
    } catch (error) {
      setSectionErrors(prev => ({
        ...prev,
        [sectionId]: error instanceof Error ? error.message : 'Erro desconhecido'
      }));
    } finally {
      setSectionLoading(prev => ({ ...prev, [sectionId]: false }));
      setRefreshingSection(null);
    }
  }, []);

  // Componentes das seções
  const sectionComponents = {
    'informacoes-empresa': <InformacoesEmpresaOrganizada />,
    'metricas-negocio': <MetricasNegocioMagic />,
    'status-plano-saas': <StatusPlanoSaasMagic />,
    'alertas-dashboard': <AlertasDashboardMagic />,
    'acoes-rapidas': <AcoesRapidasMagic />
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 ${className}`}>
      <div className="container mx-auto px-4 py-8">
        {/* Header da página */}
        <motion.div
          className="mb-8 text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <TextAnimate
            animation="blurInUp"
            className="text-4xl font-bold text-gray-900 dark:text-white mb-4"
            by="word"
          >
            🎛️ Dashboard com Seções Organizadas
          </TextAnimate>
          
          <motion.p
            className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.6 }}
          >
            Sistema avançado de organização com seções personalizáveis, métricas em tempo real e ações contextuais
          </motion.p>

          {/* Indicadores de status */}
          <motion.div
            className="flex items-center justify-center space-x-6 mt-6"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.6 }}
          >
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-600 dark:text-gray-400">Sistema Online</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {refreshingSection ? `Atualizando ${refreshingSection}...` : 'Todas as seções carregadas'}
              </span>
            </div>
          </motion.div>
        </motion.div>

        {/* Dashboard Layout Manager */}
        <DashboardLayoutManager
          sectionActions={sectionActions}
          sectionMetrics={sectionMetrics}
          sectionLoading={sectionLoading}
          sectionErrors={sectionErrors}
          lastUpdated={lastUpdated}
          onSectionRefresh={handleSectionRefresh}
        >
          {sectionComponents}
        </DashboardLayoutManager>

        {/* Footer com informações */}
        <motion.div
          className="mt-12 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1, duration: 0.6 }}
        >
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              🚀 Recursos Implementados
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 dark:text-gray-400">
              <div>✅ Seções personalizáveis</div>
              <div>✅ Métricas em tempo real</div>
              <div>✅ Ações contextuais</div>
              <div>✅ Layouts responsivos</div>
              <div>✅ Refresh automático</div>
              <div>✅ Estados de loading/erro</div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
});

DashboardSecoesOrganizadas.displayName = 'DashboardSecoesOrganizadas';

export { DashboardSecoesOrganizadas };
