'use client';

import React, { memo } from 'react';
import Link from 'next/link';
import { MagicCard } from '@/components/ui/magic-card';
import { TextAnimate } from '@/components/ui/text-animate';
import { ShimmerButton } from '@/components/ui/shimmer-button';
import { NumberTicker } from '@/components/ui/number-ticker';
import { motion } from 'framer-motion';
import { usePlanoSaas } from '@/hooks/usePlanoSaas';

interface ProgressBarProps {
  value: number;
  max: number;
  label: string;
  color: string;
  delay?: number;
}

const ProgressBar = memo(({ value, max, label, color, delay = 0 }: Readonly<ProgressBarProps>) => {
  const percentage = Math.min((value / max) * 100, 100);
  
  return (
    <motion.div
      className="space-y-2"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay }}
    >
      <div className="flex justify-between items-center">
        <span className="text-sm font-medium text-white/80">{label}</span>
        <span className="text-sm text-white/60">
          <NumberTicker value={value} delay={delay + 0.2} /> / {max}
        </span>
      </div>
      
      <div className="w-full bg-white/20 rounded-full h-2 overflow-hidden">
        <motion.div
          className={`h-full ${color} rounded-full relative overflow-hidden`}
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={{ delay: delay + 0.3, duration: 1, ease: "easeOut" }}
        >
          {/* Efeito de brilho animado */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
            animate={{ x: ['-100%', '100%'] }}
            transition={{ duration: 2, repeat: Infinity, repeatDelay: 1 }}
          />
        </motion.div>
      </div>
      
      <div className="text-xs text-white/50">
        {percentage.toFixed(1)}% utilizado
      </div>
    </motion.div>
  );
});

interface StatusPlanoSaasMagicProps {
  className?: string;
}

const StatusPlanoSaasMagicComponent = ({ className = '' }: Readonly<StatusPlanoSaasMagicProps>) => {
  const {
    planoAtual,
    limitesPlano,
    usoAtual,
    diasRestantes,
    proximoVencimento,
    loading,
    error,
    podeUpgrade,
    precisaRenovar,
    planoExpirado
  } = usePlanoSaas();

  if (loading) {
    return (
      <MagicCard className={`animate-pulse p-6 ${className}`} gradientColor="#3B82F6">
        <div className="space-y-4">
          <div className="h-6 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="space-y-2">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-2 bg-gray-200 rounded"></div>
          </div>
        </div>
      </MagicCard>
    );
  }

  if (error) {
    return (
      <MagicCard className={`border-red-200 bg-red-50 p-6 ${className}`} gradientColor="#EF4444">
        <div className="text-center space-y-4">
          <div className="text-red-600 text-4xl">💳</div>
          <div>
            <h3 className="font-semibold text-red-800 text-lg">Erro ao carregar plano</h3>
            <p className="text-sm text-red-700 mt-2">
              Não foi possível carregar as informações do seu plano SaaS.
            </p>
          </div>
        </div>
      </MagicCard>
    );
  }

  const getStatusColor = () => {
    if (planoExpirado) return '#EF4444'; // Vermelho
    if (precisaRenovar) return '#F59E0B'; // Amarelo
    return '#10B981'; // Verde
  };

  const getStatusIcon = () => {
    if (planoExpirado) return '❌';
    if (precisaRenovar) return '⚠️';
    return '✅';
  };

  const getStatusText = () => {
    if (planoExpirado) return 'Plano Expirado';
    if (precisaRenovar) return 'Renovação Necessária';
    return 'Plano Ativo';
  };

  return (
    <MagicCard 
      className={`relative overflow-hidden p-6 ${className}`}
      gradientColor={getStatusColor()}
      gradientSize={300}
    >
      <div className="relative z-10">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <TextAnimate 
            animation="slideRight" 
            className="text-xl font-bold text-white"
            by="word"
          >
            Status do Plano SaaS
          </TextAnimate>
          
          <motion.div
            className="flex items-center space-x-2"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3 }}
          >
            <span className="text-2xl">{getStatusIcon()}</span>
            <span className="text-sm font-medium text-white/90">
              {getStatusText()}
            </span>
          </motion.div>
        </div>

        {/* Informações do plano */}
        <div className="space-y-4">
          {/* Nome do plano e valor */}
          <div className="flex items-center justify-between">
            <div>
              <TextAnimate
                animation="blurInUp"
                className="text-lg font-semibold text-white"
                delay={0.1}
              >
                {planoAtual?.nome ?? 'Plano Básico'}
              </TextAnimate>
              <motion.p 
                className="text-sm text-white/70"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                {planoAtual?.descricao ?? 'Plano padrão para pequenos negócios'}
              </motion.p>
            </div>
            
            <motion.div 
              className="text-right"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              <div className="text-2xl font-bold text-white">
                R$ <NumberTicker value={planoAtual?.valor ?? 0} decimalPlaces={2} delay={0.4} />
              </div>
              <div className="text-xs text-white/60">por mês</div>
            </motion.div>
          </div>

          {/* Barras de progresso dos limites */}
          <div className="space-y-4 mt-6">
            <ProgressBar
              value={usoAtual?.agendamentos ?? 0}
              max={limitesPlano?.agendamentos ?? 100}
              label="Agendamentos"
              color="bg-blue-500"
              delay={0.5}
            />
            
            <ProgressBar
              value={usoAtual?.usuarios ?? 0}
              max={limitesPlano?.usuarios ?? 5}
              label="Usuários"
              color="bg-green-500"
              delay={0.6}
            />
            
            <ProgressBar
              value={usoAtual?.armazenamento ?? 0}
              max={limitesPlano?.armazenamento ?? 1000}
              label="Armazenamento (MB)"
              color="bg-purple-500"
              delay={0.7}
            />
          </div>

          {/* Informações de vencimento */}
          <motion.div 
            className="mt-6 p-4 bg-white/10 rounded-lg backdrop-blur-sm"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm font-medium text-white/90">Próximo Vencimento</div>
                <div className="text-xs text-white/70">
                  {proximoVencimento ? new Date(proximoVencimento).toLocaleDateString('pt-BR') : 'Não definido'}
                </div>
              </div>
              
              <div className="text-right">
                <div className="text-lg font-bold text-white">
                  <NumberTicker value={diasRestantes ?? 0} /> dias
                </div>
                <div className="text-xs text-white/70">restantes</div>
              </div>
            </div>
          </motion.div>

          {/* Ações */}
          <motion.div 
            className="flex space-x-3 mt-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.9 }}
          >
            {podeUpgrade && (
              <Link href="/proprietario/planos">
                <ShimmerButton 
                  className="flex-1"
                  shimmerColor="#60A5FA"
                  background="rgba(59, 130, 246, 0.8)"
                >
                  🚀 Fazer Upgrade
                </ShimmerButton>
              </Link>
            )}
            
            {precisaRenovar && (
              <Link href="/proprietario/pagamentos">
                <ShimmerButton 
                  className="flex-1"
                  shimmerColor="#F59E0B"
                  background="rgba(245, 158, 11, 0.8)"
                >
                  💳 Renovar Plano
                </ShimmerButton>
              </Link>
            )}
            
            <Link href="/proprietario/planos">
              <ShimmerButton 
                className="px-4"
                shimmerColor="#6B7280"
                background="rgba(107, 114, 128, 0.8)"
              >
                ⚙️ Gerenciar
              </ShimmerButton>
            </Link>
          </motion.div>
        </div>

        {/* Efeito de partículas flutuantes */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(5)].map((_, i) => (
            <motion.div
              key={`particle-${i}`}
              className="absolute w-2 h-2 bg-white rounded-full opacity-20"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -15, 0],
                opacity: [0.2, 0.6, 0.2],
                scale: [1, 1.2, 1],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>

        {/* Indicador de status no canto */}
        <motion.div
          className={`absolute top-4 right-4 w-3 h-3 rounded-full ${
            planoExpirado ? 'bg-red-400' : precisaRenovar ? 'bg-yellow-400' : 'bg-green-400'
          }`}
          animate={{ scale: [1, 1.2, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
        />
      </div>
    </MagicCard>
  );
}
