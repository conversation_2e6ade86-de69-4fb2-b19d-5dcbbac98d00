'use client';

import { useState, useEffect, useCallback } from 'react';

interface AccessibilitySettings {
  reducedMotion: boolean;
  highContrast: boolean;
  fontSize: 'small' | 'medium' | 'large' | 'extra-large';
  theme: 'light' | 'dark' | 'auto';
  screenReader: boolean;
  keyboardNavigation: boolean;
}

interface UseAccessibilityReturn {
  settings: AccessibilitySettings;
  updateSetting: <K extends keyof AccessibilitySettings>(
    key: K,
    value: AccessibilitySettings[K]
  ) => void;
  resetSettings: () => void;
  announceToScreenReader: (message: string) => void;
  focusElement: (selector: string) => void;
  skipToContent: () => void;
}

const defaultSettings: AccessibilitySettings = {
  reducedMotion: false,
  highContrast: false,
  fontSize: 'medium',
  theme: 'auto',
  screenReader: false,
  keyboardNavigation: true
};

const STORAGE_KEY = 'servicetech-accessibility-settings';

export function useAccessibility(): UseAccessibilityReturn {
  const [settings, setSettings] = useState<AccessibilitySettings>(defaultSettings);

  // Carregar configurações do localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsedSettings = JSON.parse(stored);
        setSettings({ ...defaultSettings, ...parsedSettings });
      }
    } catch (error) {
      console.warn('Erro ao carregar configurações de acessibilidade:', error);
    }
  }, []);

  // Detectar preferências do sistema
  useEffect(() => {
    // Detectar preferência de movimento reduzido
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const handleChange = (e: MediaQueryListEvent) => {
      setSettings(prev => ({ ...prev, reducedMotion: e.matches }));
    };

    if (mediaQuery.matches) {
      setSettings(prev => ({ ...prev, reducedMotion: true }));
    }

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Detectar preferência de contraste alto
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    const handleChange = (e: MediaQueryListEvent) => {
      setSettings(prev => ({ ...prev, highContrast: e.matches }));
    };

    if (mediaQuery.matches) {
      setSettings(prev => ({ ...prev, highContrast: true }));
    }

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Detectar preferência de tema
  useEffect(() => {
    if (settings.theme === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = () => {
        document.documentElement.classList.toggle('dark', mediaQuery.matches);
      };

      handleChange(); // Aplicar imediatamente
      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    } else {
      document.documentElement.classList.toggle('dark', settings.theme === 'dark');
    }
  }, [settings.theme]);

  // Aplicar configurações de fonte
  useEffect(() => {
    const fontSizes = {
      small: '14px',
      medium: '16px',
      large: '18px',
      'extra-large': '20px'
    };

    document.documentElement.style.fontSize = fontSizes[settings.fontSize];
  }, [settings.fontSize]);

  // Aplicar configurações de contraste
  useEffect(() => {
    document.documentElement.classList.toggle('high-contrast', settings.highContrast);
  }, [settings.highContrast]);

  // Salvar configurações no localStorage
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
    } catch (error) {
      console.warn('Erro ao salvar configurações de acessibilidade:', error);
    }
  }, [settings]);

  // Atualizar uma configuração específica
  const updateSetting = useCallback(<K extends keyof AccessibilitySettings>(
    key: K,
    value: AccessibilitySettings[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  }, []);

  // Resetar todas as configurações
  const resetSettings = useCallback(() => {
    setSettings(defaultSettings);
    localStorage.removeItem(STORAGE_KEY);
  }, []);

  // Anunciar mensagem para leitores de tela
  const announceToScreenReader = useCallback((message: string) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;

    document.body.appendChild(announcement);

    // Remover após um tempo para não poluir o DOM
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }, []);

  // Focar em um elemento específico
  const focusElement = useCallback((selector: string) => {
    const element = document.querySelector(selector) as HTMLElement;
    if (element) {
      element.focus();
      // Anunciar para leitores de tela
      const label = element.getAttribute('aria-label') || 
                   element.getAttribute('title') || 
                   element.textContent || 
                   'Elemento focado';
      announceToScreenReader(`Focado em: ${label}`);
    }
  }, [announceToScreenReader]);

  // Pular para o conteúdo principal
  const skipToContent = useCallback(() => {
    const mainContent = document.querySelector('main, [role="main"], #main-content');
    if (mainContent) {
      (mainContent as HTMLElement).focus();
      announceToScreenReader('Navegado para o conteúdo principal');
    }
  }, [announceToScreenReader]);

  return {
    settings,
    updateSetting,
    resetSettings,
    announceToScreenReader,
    focusElement,
    skipToContent
  };
}

// Hook para atalhos de teclado
export function useKeyboardShortcuts() {
  const { focusElement, skipToContent, announceToScreenReader } = useAccessibility();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Alt + 1: Pular para conteúdo principal
      if (event.altKey && event.key === '1') {
        event.preventDefault();
        skipToContent();
      }

      // Alt + 2: Focar na navegação
      if (event.altKey && event.key === '2') {
        event.preventDefault();
        focusElement('nav, [role="navigation"]');
      }

      // Alt + 3: Focar na busca
      if (event.altKey && event.key === '3') {
        event.preventDefault();
        focusElement('input[type="search"], [role="search"] input');
      }

      // Escape: Fechar modais/dropdowns
      if (event.key === 'Escape') {
        const activeModal = document.querySelector('[role="dialog"][aria-hidden="false"]');
        if (activeModal) {
          const closeButton = activeModal.querySelector('[aria-label*="fechar"], [aria-label*="close"]');
          if (closeButton) {
            (closeButton as HTMLElement).click();
          }
        }
      }

      // Tab: Navegação por teclado
      if (event.key === 'Tab') {
        // Adicionar indicador visual de foco se não estiver presente
        document.body.classList.add('keyboard-navigation');
      }
    };

    // Remover indicador de navegação por teclado quando usar mouse
    const handleMouseDown = () => {
      document.body.classList.remove('keyboard-navigation');
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, [focusElement, skipToContent, announceToScreenReader]);
}

// Hook para detectar se o usuário está usando leitor de tela
export function useScreenReaderDetection() {
  const [isUsingScreenReader, setIsUsingScreenReader] = useState(false);

  useEffect(() => {
    // Detectar se há um leitor de tela ativo
    const detectScreenReader = () => {
      // Verificar se há elementos com aria-live sendo lidos
      const testElement = document.createElement('div');
      testElement.setAttribute('aria-live', 'polite');
      testElement.style.position = 'absolute';
      testElement.style.left = '-10000px';
      testElement.textContent = 'Screen reader test';
      
      document.body.appendChild(testElement);
      
      setTimeout(() => {
        // Se o elemento ainda existe e tem foco, provavelmente há um leitor de tela
        if (document.activeElement === testElement) {
          setIsUsingScreenReader(true);
        }
        document.body.removeChild(testElement);
      }, 100);
    };

    detectScreenReader();
  }, []);

  return isUsingScreenReader;
}
