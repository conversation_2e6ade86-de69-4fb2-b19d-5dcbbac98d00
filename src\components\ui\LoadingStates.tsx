'use client';

import React, { memo } from 'react';
import { MagicCard } from './magic-card';
import { motion } from 'framer-motion';

interface LoadingSkeletonProps {
  className?: string;
  variant?: 'card' | 'list' | 'metrics' | 'dashboard';
  count?: number;
}

const LoadingSkeletonComponent = ({ 
  className = '', 
  variant = 'card',
  count = 1 
}: Readonly<LoadingSkeletonProps>) => {
  const renderCardSkeleton = () => (
    <MagicCard className={`animate-pulse p-6 ${className}`}>
      <div className="space-y-4">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
        </div>
        <div className="space-y-2">
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
        </div>
      </div>
    </MagicCard>
  );

  const renderMetricsSkeleton = () => (
    <MagicCard className={`animate-pulse p-6 ${className}`}>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div className="w-12 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
        <div className="h-1 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
    </MagicCard>
  );

  const renderListSkeleton = () => (
    <div className={`space-y-3 ${className}`}>
      {[...Array(count)].map((_, i) => (
        <div key={`list-skeleton-${i}`} className="animate-pulse">
          <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            </div>
            <div className="w-16 h-6 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      ))}
    </div>
  );

  const renderDashboardSkeleton = () => (
    <div className={`space-y-6 ${className}`}>
      {/* Header skeleton */}
      <div className="space-y-2">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 animate-pulse"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 animate-pulse"></div>
      </div>
      
      {/* Grid skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={`dashboard-skeleton-${i}`}>
            {renderMetricsSkeleton()}
          </div>
        ))}
      </div>
    </div>
  );

  const renderSkeleton = () => {
    switch (variant) {
      case 'metrics':
        return renderMetricsSkeleton();
      case 'list':
        return renderListSkeleton();
      case 'dashboard':
        return renderDashboardSkeleton();
      default:
        return renderCardSkeleton();
    }
  };

  if (count > 1 && variant !== 'list' && variant !== 'dashboard') {
    return (
      <div className="space-y-4">
        {[...Array(count)].map((_, i) => (
          <div key={`skeleton-${i}`}>
            {renderSkeleton()}
          </div>
        ))}
      </div>
    );
  }

  return renderSkeleton();
};

export const LoadingSkeleton = memo(LoadingSkeletonComponent);

// Componente de erro otimizado
interface ErrorStateProps {
  title?: string;
  message?: string;
  icon?: string;
  onRetry?: () => void;
  className?: string;
}

const ErrorStateComponent = ({ 
  title = 'Erro ao carregar dados',
  message = 'Não foi possível carregar as informações. Tente novamente.',
  icon = '⚠️',
  onRetry,
  className = ''
}: Readonly<ErrorStateProps>) => {
  return (
    <MagicCard className={`border-red-200 bg-red-50 dark:bg-red-900/20 p-6 ${className}`} gradientColor="#EF4444">
      <div className="text-center space-y-4">
        <motion.div 
          className="text-red-600 dark:text-red-400 text-4xl"
          animate={{ scale: [1, 1.1, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          {icon}
        </motion.div>
        <div>
          <h3 className="font-semibold text-red-800 dark:text-red-200 text-lg">{title}</h3>
          <p className="text-sm text-red-700 dark:text-red-300 mt-2">
            {message}
          </p>
        </div>
        {onRetry && (
          <motion.button
            onClick={onRetry}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm font-medium transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Tentar Novamente
          </motion.button>
        )}
      </div>
    </MagicCard>
  );
};

export const ErrorState = memo(ErrorStateComponent);

// Componente de estado vazio otimizado
interface EmptyStateProps {
  title?: string;
  message?: string;
  icon?: string;
  actionText?: string;
  onAction?: () => void;
  className?: string;
}

const EmptyStateComponent = ({ 
  title = 'Nenhum dado encontrado',
  message = 'Não há informações para exibir no momento.',
  icon = '📭',
  actionText,
  onAction,
  className = ''
}: Readonly<EmptyStateProps>) => {
  return (
    <MagicCard className={`border-gray-200 bg-gray-50 dark:bg-gray-800 p-6 ${className}`} gradientColor="#6B7280">
      <div className="text-center space-y-4">
        <motion.div 
          className="text-gray-400 text-4xl"
          animate={{ y: [0, -5, 0] }}
          transition={{ duration: 3, repeat: Infinity }}
        >
          {icon}
        </motion.div>
        <div>
          <h3 className="font-semibold text-gray-700 dark:text-gray-300 text-lg">{title}</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
            {message}
          </p>
        </div>
        {actionText && onAction && (
          <motion.button
            onClick={onAction}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {actionText}
          </motion.button>
        )}
      </div>
    </MagicCard>
  );
};

export const EmptyState = memo(EmptyStateComponent);

// Hook para estados de loading com debounce
export function useLoadingState(loading: boolean, delay = 200) {
  const [debouncedLoading, setDebouncedLoading] = React.useState(loading);

  React.useEffect(() => {
    if (loading) {
      setDebouncedLoading(true);
    } else {
      const timer = setTimeout(() => {
        setDebouncedLoading(false);
      }, delay);
      return () => clearTimeout(timer);
    }
  }, [loading, delay]);

  return debouncedLoading;
}
