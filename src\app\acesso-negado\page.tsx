'use client';

import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/Button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';

export default function AcessoNegadoPage() {
  const router = useRouter();
  const { user, signOut } = useAuth();

  const handleGoBack = () => {
    if (user) {
      // Redirecionar baseado no papel do usuário
      switch (user.role) {
        case 'Administrador':
          router.push('/admin/dashboard');
          break;
        case 'Proprietario':
          router.push('/proprietario/dashboard-magic');
          break;
        case 'Colaborador':
          router.push('/colaborador/agenda');
          break;
        default:
          router.push('/cliente/dashboard');
          break;
      }
    } else {
      router.push('/');
    }
  };

  const handleLogout = async () => {
    await signOut();
    router.push('/');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-[var(--background)] p-4">
      <Card className="max-w-md w-full">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
            </svg>
          </div>
          <CardTitle className="text-2xl font-bold text-[var(--text-primary)]">
            Acesso Negado
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-[var(--text-secondary)]">
            Você não tem permissão para acessar esta página.
          </p>
          
          {user && (
            <div className="bg-[var(--surface)] p-4 rounded-lg">
              <p className="text-sm text-[var(--text-secondary)] mb-1">
                Logado como:
              </p>
              <p className="font-medium text-[var(--text-primary)]">
                {user.name || user.email}
              </p>
              <p className="text-sm text-[var(--text-secondary)]">
                Papel: {user.role}
              </p>
            </div>
          )}
          
          <div className="flex flex-col gap-3 pt-4">
            <Button 
              onClick={handleGoBack}
              variant="primary"
              className="w-full"
            >
              Voltar ao Dashboard
            </Button>
            
            {user && (
              <Button 
                onClick={handleLogout}
                variant="outline"
                className="w-full"
              >
                Fazer Logout
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
