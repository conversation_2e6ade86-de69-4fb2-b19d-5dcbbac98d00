'use client';

import React, { memo, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MagicCard } from './magic-card';
import { TextAnimate } from './text-animate';
import { ShimmerButton } from './shimmer-button';

interface DashboardSectionProps {
  title: string;
  description?: string;
  icon?: string;
  children: React.ReactNode;
  className?: string;
  gradientColor?: string;
  collapsible?: boolean;
  defaultExpanded?: boolean;
  actions?: Array<{
    label: string;
    onClick: () => void;
    variant?: 'primary' | 'secondary' | 'danger';
    icon?: string;
  }>;
  headerContent?: React.ReactNode;
}

const DashboardSection = memo(({
  title,
  description,
  icon,
  children,
  className = '',
  gradientColor = '#6366F1',
  collapsible = false,
  defaultExpanded = true,
  actions = [],
  headerContent
}: Readonly<DashboardSectionProps>) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  const getActionVariant = (variant: string = 'primary') => {
    switch (variant) {
      case 'danger':
        return {
          background: '#EF444420',
          shimmerColor: '#EF4444',
          textColor: 'text-red-600 dark:text-red-400'
        };
      case 'secondary':
        return {
          background: '#6B728020',
          shimmerColor: '#6B7280',
          textColor: 'text-gray-600 dark:text-gray-400'
        };
      default:
        return {
          background: `${gradientColor}20`,
          shimmerColor: gradientColor,
          textColor: 'text-blue-600 dark:text-blue-400'
        };
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={className}
    >
      <MagicCard
        className="overflow-hidden"
        gradientColor={gradientColor}
        gradientSize={200}
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {icon && (
                <motion.div
                  className="text-2xl"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  {icon}
                </motion.div>
              )}
              
              <div className="flex-1">
                <div className="flex items-center space-x-3">
                  <TextAnimate
                    animation="blurInUp"
                    className="text-xl font-semibold text-gray-900 dark:text-white"
                  >
                    {title}
                  </TextAnimate>
                  
                  {collapsible && (
                    <motion.button
                      onClick={() => setIsExpanded(!isExpanded)}
                      className="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <motion.div
                        animate={{ rotate: isExpanded ? 180 : 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        ⌄
                      </motion.div>
                    </motion.button>
                  )}
                </div>
                
                {description && (
                  <motion.p
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.2 }}
                    className="text-sm text-gray-600 dark:text-gray-400 mt-1"
                  >
                    {description}
                  </motion.p>
                )}
              </div>
            </div>

            {/* Header Content */}
            {headerContent && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
              >
                {headerContent}
              </motion.div>
            )}

            {/* Actions */}
            {actions.length > 0 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
                className="flex items-center space-x-2"
              >
                {actions.map((action, index) => {
                  const variant = getActionVariant(action.variant);
                  
                  return (
                    <ShimmerButton
                      key={`action-${index}`}
                      onClick={action.onClick}
                      className={`text-xs px-3 py-2 ${variant.textColor}`}
                      shimmerColor={variant.shimmerColor}
                      background={variant.background}
                    >
                      {action.icon && <span className="mr-1">{action.icon}</span>}
                      {action.label}
                    </ShimmerButton>
                  );
                })}
              </motion.div>
            )}
          </div>
        </div>

        {/* Content */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden"
            >
              <div className="p-6">
                {children}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </MagicCard>
    </motion.div>
  );
});

DashboardSection.displayName = 'DashboardSection';

// Componente de grid responsivo para seções
interface DashboardGridProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3 | 4;
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const DashboardGrid = memo(({
  children,
  columns = 3,
  gap = 'md',
  className = ''
}: Readonly<DashboardGridProps>) => {
  const getGridClasses = () => {
    const columnClasses = {
      1: 'grid-cols-1',
      2: 'grid-cols-1 lg:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
    };

    const gapClasses = {
      sm: 'gap-4',
      md: 'gap-6',
      lg: 'gap-8'
    };

    return `grid ${columnClasses[columns]} ${gapClasses[gap]}`;
  };

  return (
    <div className={`${getGridClasses()} ${className}`}>
      {children}
    </div>
  );
});

DashboardGrid.displayName = 'DashboardGrid';

// Componente de estatísticas rápidas
interface QuickStatsProps {
  stats: Array<{
    label: string;
    value: string | number;
    change?: number;
    icon?: string;
    color?: string;
  }>;
  className?: string;
}

export const QuickStats = memo(({
  stats,
  className = ''
}: Readonly<QuickStatsProps>) => {
  return (
    <div className={`grid grid-cols-2 md:grid-cols-4 gap-4 ${className}`}>
      {stats.map((stat, index) => (
        <motion.div
          key={`stat-${index}`}
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: index * 0.1 }}
          className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center justify-between">
            {stat.icon && (
              <div className="text-lg" style={{ color: stat.color }}>
                {stat.icon}
              </div>
            )}
            {stat.change !== undefined && (
              <div className={`text-xs px-2 py-1 rounded ${
                stat.change > 0 
                  ? 'text-green-700 bg-green-100 dark:text-green-400 dark:bg-green-900/30'
                  : stat.change < 0
                  ? 'text-red-700 bg-red-100 dark:text-red-400 dark:bg-red-900/30'
                  : 'text-gray-700 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/30'
              }`}>
                {stat.change > 0 ? '+' : ''}{stat.change}%
              </div>
            )}
          </div>
          
          <div className="mt-2">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {stat.value}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {stat.label}
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
});

QuickStats.displayName = 'QuickStats';

export { DashboardSection };
