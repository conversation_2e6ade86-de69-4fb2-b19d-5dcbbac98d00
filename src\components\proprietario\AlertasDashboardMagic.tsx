'use client';

import React, { useState, memo, useCallback } from 'react';
import { MagicCard } from '@/components/ui/magic-card';
import { TextAnimate } from '@/components/ui/text-animate';
import { ShimmerButton } from '@/components/ui/shimmer-button';
import { motion, AnimatePresence } from 'framer-motion';
import { useAlertas } from '@/hooks/useAlertas';

interface AlertaItemProps {
  id: string;
  tipo: 'info' | 'warning' | 'error' | 'success';
  titulo: string;
  mensagem: string;
  timestamp: Date;
  acao?: {
    texto: string;
    href?: string;
    onClick?: () => void;
  };
  onDismiss?: (id: string) => void;
}

const AlertaItem = memo(({
  id,
  tipo,
  titulo,
  mensagem,
  timestamp,
  acao,
  onDismiss
}: Readonly<AlertaItemProps>) => {
  const getAlertaConfig = () => {
    switch (tipo) {
      case 'error':
        return {
          icon: '🚨',
          bgColor: 'bg-red-50 dark:bg-red-900/20',
          borderColor: 'border-red-200 dark:border-red-800',
          textColor: 'text-red-800 dark:text-red-200',
          gradientColor: '#EF4444'
        };
      case 'warning':
        return {
          icon: '⚠️',
          bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
          borderColor: 'border-yellow-200 dark:border-yellow-800',
          textColor: 'text-yellow-800 dark:text-yellow-200',
          gradientColor: '#F59E0B'
        };
      case 'success':
        return {
          icon: '✅',
          bgColor: 'bg-green-50 dark:bg-green-900/20',
          borderColor: 'border-green-200 dark:border-green-800',
          textColor: 'text-green-800 dark:text-green-200',
          gradientColor: '#10B981'
        };
      default:
        return {
          icon: 'ℹ️',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          borderColor: 'border-blue-200 dark:border-blue-800',
          textColor: 'text-blue-800 dark:text-blue-200',
          gradientColor: '#3B82F6'
        };
    }
  };

  const config = getAlertaConfig();

  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.8, y: 20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.8, y: -20 }}
      whileHover={{ scale: 1.02 }}
      className="w-full"
    >
      <MagicCard 
        className={`p-4 ${config.bgColor} border ${config.borderColor} hover:shadow-lg transition-all duration-300`}
        gradientColor={config.gradientColor}
        gradientSize={200}
      >
        <div className="flex items-start space-x-3">
          {/* Ícone */}
          <motion.div 
            className="text-2xl flex-shrink-0"
            animate={{ rotate: [0, 10, -10, 0] }}
            transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
          >
            {config.icon}
          </motion.div>

          {/* Conteúdo */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className={`font-semibold ${config.textColor} text-sm`}>
                  {titulo}
                </h4>
                <p className={`${config.textColor} text-xs mt-1 opacity-80`}>
                  {mensagem}
                </p>
                <p className={`${config.textColor} text-xs mt-2 opacity-60`}>
                  {timestamp.toLocaleString('pt-BR')}
                </p>
              </div>

              {/* Botão de fechar */}
              {onDismiss && (
                <motion.button
                  onClick={() => onDismiss(id)}
                  className={`${config.textColor} opacity-50 hover:opacity-100 transition-opacity ml-2`}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  ✕
                </motion.button>
              )}
            </div>

            {/* Ação */}
            {acao && (
              <motion.div 
                className="mt-3"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                {acao.href ? (
                  <a href={acao.href}>
                    <ShimmerButton 
                      className="text-xs px-3 py-1"
                      shimmerColor={config.gradientColor}
                      background={`${config.gradientColor}80`}
                    >
                      {acao.texto}
                    </ShimmerButton>
                  </a>
                ) : (
                  <ShimmerButton 
                    onClick={acao.onClick}
                    className="text-xs px-3 py-1"
                    shimmerColor={config.gradientColor}
                    background={`${config.gradientColor}80`}
                  >
                    {acao.texto}
                  </ShimmerButton>
                )}
              </motion.div>
            )}
          </div>
        </div>

        {/* Barra de progresso para alertas temporários */}
        {tipo === 'info' && (
          <motion.div 
            className="mt-3 h-1 bg-gray-200 rounded-full overflow-hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <motion.div 
              className="h-full bg-blue-500 rounded-full"
              initial={{ width: '100%' }}
              animate={{ width: '0%' }}
              transition={{ duration: 10, ease: "linear" }}
            />
          </motion.div>
        )}
      </MagicCard>
    </motion.div>
  );
});

interface AlertasDashboardMagicProps {
  className?: string;
}

const AlertasDashboardMagicComponent = ({ className = '' }: Readonly<AlertasDashboardMagicProps>) => {
  const {
    alertas,
    loading,
    error,
    marcarComoLido,
    dismissAlert,
    alertasNaoLidos
  } = useAlertas();

  const [filtroTipo, setFiltroTipo] = useState<'todos' | 'error' | 'warning' | 'info' | 'success'>('todos');

  const handleFiltroChange = useCallback((tipo: 'todos' | 'error' | 'warning' | 'info' | 'success') => {
    setFiltroTipo(tipo);
  }, []);

  const handleMarcarTodosLidos = useCallback(() => {
    marcarComoLido('todos');
  }, [marcarComoLido]);

  const alertasFiltrados = alertas.filter(alerta => 
    filtroTipo === 'todos' || alerta.tipo === filtroTipo
  );

  if (loading) {
    return (
      <MagicCard className={`animate-pulse p-6 ${className}`} gradientColor="#3B82F6">
        <div className="space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/2"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={`loading-alert-${i}`} className="flex space-x-3">
                <div className="w-8 h-8 bg-gray-200 rounded"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </MagicCard>
    );
  }

  if (error) {
    return (
      <MagicCard className={`border-red-200 bg-red-50 p-6 ${className}`} gradientColor="#EF4444">
        <div className="text-center space-y-4">
          <div className="text-red-600 text-4xl">🔔</div>
          <div>
            <h3 className="font-semibold text-red-800 text-lg">Erro ao carregar alertas</h3>
            <p className="text-sm text-red-700 mt-2">
              Não foi possível carregar os alertas. Tente novamente.
            </p>
          </div>
        </div>
      </MagicCard>
    );
  }

  return (
    <div className={className}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <TextAnimate
            animation="slideRight"
            className="text-2xl font-bold text-gray-900 dark:text-white"
            by="word"
          >
            Alertas e Notificações
          </TextAnimate>
          
          {alertasNaoLidos > 0 && (
            <motion.div
              className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full"
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              {alertasNaoLidos}
            </motion.div>
          )}
        </div>

        {/* Filtros */}
        <div className="flex space-x-2">
          {['todos', 'error', 'warning', 'info', 'success'].map((tipo) => (
            <motion.button
              key={tipo}
              onClick={() => handleFiltroChange(tipo as any)}
              className={`px-3 py-1 text-xs rounded-full transition-colors ${
                filtroTipo === tipo
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {tipo.charAt(0).toUpperCase() + tipo.slice(1)}
            </motion.button>
          ))}
        </div>
      </div>

      {/* Lista de alertas */}
      {alertasFiltrados.length === 0 ? (
        <MagicCard className="p-8 text-center" gradientColor="#10B981">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            <div className="text-6xl">🎉</div>
            <TextAnimate
              animation="blurInUp"
              className="text-lg font-semibold text-gray-700 dark:text-gray-300"
            >
              Nenhum alerta encontrado
            </TextAnimate>
            <p className="text-gray-500 dark:text-gray-400">
              Tudo está funcionando perfeitamente!
            </p>
          </motion.div>
        </MagicCard>
      ) : (
        <div className="space-y-4">
          <AnimatePresence mode="popLayout">
            {alertasFiltrados.map((alerta) => (
              <AlertaItem
                key={alerta.id}
                {...alerta}
                onDismiss={dismissAlert}
              />
            ))}
          </AnimatePresence>
        </div>
      )}

      {/* Ação para marcar todos como lidos */}
      {alertasNaoLidos > 0 && (
        <motion.div 
          className="mt-6 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <ShimmerButton 
            onClick={handleMarcarTodosLidos}
            className="px-6 py-2"
            shimmerColor="#6B7280"
            background="rgba(107, 114, 128, 0.8)"
          >
            ✓ Marcar Todos como Lidos
          </ShimmerButton>
        </motion.div>
      )}

      {/* Efeito de partículas de notificação */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {alertasNaoLidos > 0 && [...Array(3)].map((_, i) => (
          <motion.div
            key={`notification-particle-${i}`}
            className="absolute w-1 h-1 bg-red-400 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -30, 0],
              opacity: [0.5, 1, 0.5],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>
    </div>
  );
};

// Memoizar componente para evitar re-renders desnecessários
export const AlertasDashboardMagic = memo(AlertasDashboardMagicComponent, (prevProps, nextProps) => {
  return prevProps.className === nextProps.className;
});
