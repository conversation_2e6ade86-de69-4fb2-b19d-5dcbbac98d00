-- Políticas RLS (Row Level Security) aprimoradas para ServiceTech

-- =====================================================
-- EMPRESAS - Políticas de Segurança
-- =====================================================

-- Habilitar RLS na tabela empresas
ALTER TABLE empresas ENABLE ROW LEVEL SECURITY;

-- Política para proprietários verem apenas suas empresas
CREATE POLICY "proprietarios_podem_ver_suas_empresas" ON empresas
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_roles ur
      JOIN roles r ON ur.role_id = r.role_id
      WHERE ur.user_id = auth.uid()
        AND ur.empresa_id = empresas.empresa_id
        AND r.nome_role = 'proprietario'
        AND ur.ativo = true
    )
  );

-- Política para proprietários editarem suas empresas
CREATE POLICY "proprietarios_podem_editar_suas_empresas" ON empresas
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM user_roles ur
      JOIN roles r ON ur.role_id = r.role_id
      WHERE ur.user_id = auth.uid()
        AND ur.empresa_id = empresas.empresa_id
        AND r.nome_role = 'proprietario'
        AND ur.ativo = true
    )
  );

-- Política para colaboradores verem empresa onde trabalham
CREATE POLICY "colaboradores_podem_ver_empresa" ON empresas
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM colaboradores_empresa ce
      WHERE ce.user_id = auth.uid()
        AND ce.empresa_id = empresas.empresa_id
        AND ce.ativo = true
    )
  );

-- =====================================================
-- SERVIÇOS - Políticas de Segurança
-- =====================================================

ALTER TABLE servicos ENABLE ROW LEVEL SECURITY;

-- Política para usuários da empresa verem serviços
CREATE POLICY "usuarios_empresa_podem_ver_servicos" ON servicos
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_roles ur
      WHERE ur.user_id = auth.uid()
        AND ur.empresa_id = servicos.empresa_id
        AND ur.ativo = true
    )
    OR
    EXISTS (
      SELECT 1 FROM colaboradores_empresa ce
      WHERE ce.user_id = auth.uid()
        AND ce.empresa_id = servicos.empresa_id
        AND ce.ativo = true
    )
  );

-- Política para proprietários e gerentes gerenciarem serviços
CREATE POLICY "proprietarios_gerentes_podem_gerenciar_servicos" ON servicos
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM user_roles ur
      JOIN roles r ON ur.role_id = r.role_id
      WHERE ur.user_id = auth.uid()
        AND ur.empresa_id = servicos.empresa_id
        AND r.nome_role IN ('proprietario', 'gerente')
        AND ur.ativo = true
    )
  );

-- Política para colaboradores com permissão gerenciarem serviços
CREATE POLICY "colaboradores_com_permissao_podem_gerenciar_servicos" ON servicos
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM colaboradores_empresa ce
      WHERE ce.user_id = auth.uid()
        AND ce.empresa_id = servicos.empresa_id
        AND ce.ativo = true
        AND (ce.permissoes->>'pode_gerenciar_servicos')::boolean = true
    )
  );

-- =====================================================
-- AGENDAMENTOS - Políticas de Segurança
-- =====================================================

ALTER TABLE agendamentos ENABLE ROW LEVEL SECURITY;

-- Política para usuários da empresa verem agendamentos
CREATE POLICY "usuarios_empresa_podem_ver_agendamentos" ON agendamentos
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_roles ur
      WHERE ur.user_id = auth.uid()
        AND ur.empresa_id = agendamentos.empresa_id
        AND ur.ativo = true
    )
    OR
    EXISTS (
      SELECT 1 FROM colaboradores_empresa ce
      WHERE ce.user_id = auth.uid()
        AND ce.empresa_id = agendamentos.empresa_id
        AND ce.ativo = true
    )
  );

-- Política para clientes verem apenas seus agendamentos
CREATE POLICY "clientes_podem_ver_seus_agendamentos" ON agendamentos
  FOR SELECT
  USING (cliente_id = auth.uid());

-- Política para proprietários e gerentes gerenciarem agendamentos
CREATE POLICY "proprietarios_gerentes_podem_gerenciar_agendamentos" ON agendamentos
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM user_roles ur
      JOIN roles r ON ur.role_id = r.role_id
      WHERE ur.user_id = auth.uid()
        AND ur.empresa_id = agendamentos.empresa_id
        AND r.nome_role IN ('proprietario', 'gerente')
        AND ur.ativo = true
    )
  );

-- Política para colaboradores gerenciarem seus agendamentos
CREATE POLICY "colaboradores_podem_gerenciar_seus_agendamentos" ON agendamentos
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM colaboradores_empresa ce
      WHERE ce.user_id = auth.uid()
        AND ce.empresa_id = agendamentos.empresa_id
        AND ce.colaborador_id = agendamentos.colaborador_id
        AND ce.ativo = true
    )
  );

-- Política para colaboradores com permissão criarem agendamentos
CREATE POLICY "colaboradores_com_permissao_podem_criar_agendamentos" ON agendamentos
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM colaboradores_empresa ce
      WHERE ce.user_id = auth.uid()
        AND ce.empresa_id = agendamentos.empresa_id
        AND ce.ativo = true
        AND (ce.permissoes->>'pode_criar_agendamento')::boolean = true
    )
  );

-- =====================================================
-- COLABORADORES_EMPRESA - Políticas de Segurança
-- =====================================================

ALTER TABLE colaboradores_empresa ENABLE ROW LEVEL SECURITY;

-- Política para proprietários verem colaboradores da empresa
CREATE POLICY "proprietarios_podem_ver_colaboradores" ON colaboradores_empresa
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_roles ur
      JOIN roles r ON ur.role_id = r.role_id
      WHERE ur.user_id = auth.uid()
        AND ur.empresa_id = colaboradores_empresa.empresa_id
        AND r.nome_role = 'proprietario'
        AND ur.ativo = true
    )
  );

-- Política para colaboradores verem próprios dados
CREATE POLICY "colaboradores_podem_ver_proprios_dados" ON colaboradores_empresa
  FOR SELECT
  USING (user_id = auth.uid());

-- Política para proprietários gerenciarem colaboradores
CREATE POLICY "proprietarios_podem_gerenciar_colaboradores" ON colaboradores_empresa
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM user_roles ur
      JOIN roles r ON ur.role_id = r.role_id
      WHERE ur.user_id = auth.uid()
        AND ur.empresa_id = colaboradores_empresa.empresa_id
        AND r.nome_role = 'proprietario'
        AND ur.ativo = true
    )
  );

-- =====================================================
-- USER_ROLES - Políticas de Segurança
-- =====================================================

ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;

-- Política para usuários verem próprios roles
CREATE POLICY "usuarios_podem_ver_proprios_roles" ON user_roles
  FOR SELECT
  USING (user_id = auth.uid());

-- Política para proprietários verem roles da empresa
CREATE POLICY "proprietarios_podem_ver_roles_empresa" ON user_roles
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_roles ur
      JOIN roles r ON ur.role_id = r.role_id
      WHERE ur.user_id = auth.uid()
        AND ur.empresa_id = user_roles.empresa_id
        AND r.nome_role = 'proprietario'
        AND ur.ativo = true
    )
  );

-- Política para proprietários gerenciarem roles
CREATE POLICY "proprietarios_podem_gerenciar_roles" ON user_roles
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM user_roles ur
      JOIN roles r ON ur.role_id = r.role_id
      WHERE ur.user_id = auth.uid()
        AND ur.empresa_id = user_roles.empresa_id
        AND r.nome_role = 'proprietario'
        AND ur.ativo = true
    )
  );

-- =====================================================
-- FUNÇÕES DE SEGURANÇA AUXILIARES
-- =====================================================

-- Função para verificar se usuário é proprietário da empresa
CREATE OR REPLACE FUNCTION is_empresa_owner(empresa_id_param INTEGER)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM user_roles ur
    JOIN roles r ON ur.role_id = r.role_id
    WHERE ur.user_id = auth.uid()
      AND ur.empresa_id = empresa_id_param
      AND r.nome_role = 'proprietario'
      AND ur.ativo = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para verificar se usuário é colaborador da empresa
CREATE OR REPLACE FUNCTION is_empresa_colaborador(empresa_id_param INTEGER)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM colaboradores_empresa ce
    WHERE ce.user_id = auth.uid()
      AND ce.empresa_id = empresa_id_param
      AND ce.ativo = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para verificar permissão específica do colaborador
CREATE OR REPLACE FUNCTION has_colaborador_permission(
  empresa_id_param INTEGER,
  permission_name TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM colaboradores_empresa ce
    WHERE ce.user_id = auth.uid()
      AND ce.empresa_id = empresa_id_param
      AND ce.ativo = true
      AND (ce.permissoes->>permission_name)::boolean = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para verificar se usuário tem role específico
CREATE OR REPLACE FUNCTION has_role(role_name_param TEXT, empresa_id_param INTEGER DEFAULT NULL)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM user_roles ur
    JOIN roles r ON ur.role_id = r.role_id
    WHERE ur.user_id = auth.uid()
      AND r.nome_role = role_name_param
      AND (empresa_id_param IS NULL OR ur.empresa_id = empresa_id_param)
      AND ur.ativo = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- POLÍTICAS PARA VIEWS
-- =====================================================

-- View para dashboard de métricas
CREATE OR REPLACE VIEW view_dashboard_metricas AS
SELECT 
  e.empresa_id,
  COUNT(CASE WHEN a.created_at >= date_trunc('month', CURRENT_DATE) THEN 1 END) as total_agendamentos_mes,
  COUNT(CASE WHEN a.status = 'pendente' THEN 1 END) as agendamentos_pendentes,
  COUNT(CASE WHEN a.status = 'confirmado' THEN 1 END) as agendamentos_confirmados,
  COUNT(CASE WHEN a.status = 'concluido' THEN 1 END) as agendamentos_concluidos,
  COUNT(CASE WHEN a.status = 'cancelado' THEN 1 END) as agendamentos_cancelados,
  COALESCE(SUM(CASE WHEN a.created_at >= date_trunc('month', CURRENT_DATE) THEN a.valor_total END), 0) as receita_bruta_mes,
  COALESCE(SUM(CASE WHEN a.status = 'concluido' AND a.created_at >= date_trunc('month', CURRENT_DATE) THEN a.valor_total END), 0) as receita_liquida_mes,
  COUNT(DISTINCT a.cliente_id) as total_clientes_ativos,
  CASE 
    WHEN COUNT(CASE WHEN a.created_at >= date_trunc('month', CURRENT_DATE) THEN 1 END) > 0 
    THEN (COUNT(CASE WHEN a.status = 'confirmado' THEN 1 END)::float / COUNT(CASE WHEN a.created_at >= date_trunc('month', CURRENT_DATE) THEN 1 END)::float) * 100
    ELSE 0 
  END as taxa_confirmacao_mes,
  CASE 
    WHEN COUNT(CASE WHEN a.created_at >= date_trunc('month', CURRENT_DATE) THEN 1 END) > 0 
    THEN (COUNT(CASE WHEN a.status = 'cancelado' THEN 1 END)::float / COUNT(CASE WHEN a.created_at >= date_trunc('month', CURRENT_DATE) THEN 1 END)::float) * 100
    ELSE 0 
  END as taxa_cancelamento_mes,
  CASE 
    WHEN COUNT(CASE WHEN a.status = 'concluido' THEN 1 END) > 0 
    THEN COALESCE(SUM(CASE WHEN a.status = 'concluido' THEN a.valor_total END), 0) / COUNT(CASE WHEN a.status = 'concluido' THEN 1 END)
    ELSE 0 
  END as ticket_medio,
  0 as crescimento_mes_anterior -- Implementar cálculo de crescimento
FROM empresas e
LEFT JOIN agendamentos a ON e.empresa_id = a.empresa_id
GROUP BY e.empresa_id;

-- RLS para view de métricas
ALTER VIEW view_dashboard_metricas ENABLE ROW LEVEL SECURITY;

CREATE POLICY "usuarios_empresa_podem_ver_metricas" ON view_dashboard_metricas
  FOR SELECT
  USING (
    is_empresa_owner(empresa_id) OR 
    is_empresa_colaborador(empresa_id)
  );

-- =====================================================
-- TRIGGERS PARA AUDITORIA
-- =====================================================

-- Função para auditoria
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
  -- Log de mudanças importantes
  IF TG_OP = 'UPDATE' THEN
    INSERT INTO audit_log (
      table_name,
      operation,
      old_values,
      new_values,
      user_id,
      timestamp
    ) VALUES (
      TG_TABLE_NAME,
      TG_OP,
      row_to_json(OLD),
      row_to_json(NEW),
      auth.uid(),
      NOW()
    );
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    INSERT INTO audit_log (
      table_name,
      operation,
      old_values,
      user_id,
      timestamp
    ) VALUES (
      TG_TABLE_NAME,
      TG_OP,
      row_to_json(OLD),
      auth.uid(),
      NOW()
    );
    RETURN OLD;
  ELSIF TG_OP = 'INSERT' THEN
    INSERT INTO audit_log (
      table_name,
      operation,
      new_values,
      user_id,
      timestamp
    ) VALUES (
      TG_TABLE_NAME,
      TG_OP,
      row_to_json(NEW),
      auth.uid(),
      NOW()
    );
    RETURN NEW;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Aplicar triggers de auditoria nas tabelas principais
CREATE TRIGGER audit_empresas_trigger
  AFTER INSERT OR UPDATE OR DELETE ON empresas
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_agendamentos_trigger
  AFTER INSERT OR UPDATE OR DELETE ON agendamentos
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
