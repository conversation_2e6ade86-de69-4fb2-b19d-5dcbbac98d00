import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock dos hooks
jest.mock('@/hooks/useEmpresaProprietario', () => ({
  useEmpresaProprietario: () => ({
    empresa: {
      empresa_id: 1,
      nome: 'Empresa Teste',
      email: '<EMAIL>',
      telefone: '(11) 99999-9999',
      status: 'ativo'
    },
    planoSaas: {
      nome: 'Plano Premium',
      valor: 99.90,
      status: 'ativo'
    },
    metricas: {
      totalAgendamentos: 150,
      receitaMensal: 5000,
      clientesAtivos: 45
    },
    loading: false,
    error: null,
    temEmpresa: true,
    empresaAtiva: true
  })
}));

jest.mock('@/hooks/useMetricasNegocio', () => ({
  useMetricasNegocio: () => ({
    totalAgendamentos: 150,
    agendamentosHoje: 12,
    receitaMensal: 5000,
    clientesAtivos: 45,
    taxaCancelamento: 5.2,
    avaliacaoMedia: 4.8,
    loading: false,
    error: null
  })
}));

jest.mock('@/hooks/useResponsiveConfig', () => ({
  useResponsiveConfig: () => ({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    prefersReducedMotion: false
  })
}));

jest.mock('@/hooks/useAccessibility', () => ({
  useAccessibility: () => ({
    settings: {
      reducedMotion: false,
      highContrast: false,
      fontSize: 'medium',
      theme: 'light',
      screenReader: false,
      keyboardNavigation: true
    },
    updateSetting: jest.fn(),
    resetSettings: jest.fn(),
    announceToScreenReader: jest.fn(),
    focusElement: jest.fn(),
    skipToContent: jest.fn()
  }),
  useKeyboardShortcuts: () => {}
}));

// Mock do Framer Motion para evitar problemas de animação nos testes
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
  useAnimation: () => ({
    start: jest.fn(),
    stop: jest.fn()
  })
}));

// Importar componentes após os mocks
import { MetricasNegocioMagic } from '../MetricasNegocioMagic';
import { InformacoesEmpresaMagic } from '../InformacoesEmpresaMagic';

describe('Dashboard Magic Improved', () => {
  describe('MetricasNegocioMagic', () => {
    it('deve renderizar as métricas com os valores corretos', () => {
      render(<MetricasNegocioMagic />);

      // Verificar se as métricas estão sendo exibidas
      expect(screen.getByText('150')).toBeInTheDocument(); // Total agendamentos
      expect(screen.getByText('12')).toBeInTheDocument();  // Agendamentos hoje
      expect(screen.getByText('5000')).toBeInTheDocument(); // Receita mensal
      expect(screen.getByText('45')).toBeInTheDocument();   // Clientes ativos
    });
  });

  describe('InformacoesEmpresaMagic', () => {
    it('deve renderizar as informações da empresa', () => {
      render(<InformacoesEmpresaMagic />);

      expect(screen.getByText('Empresa Teste')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('(11) 99999-9999')).toBeInTheDocument();
    });
  });

  describe('Performance e Otimizações', () => {
    it('deve renderizar componentes sem erros', () => {
      // Teste básico para verificar se os componentes renderizam
      expect(() => {
        render(<MetricasNegocioMagic />);
      }).not.toThrow();

      expect(() => {
        render(<InformacoesEmpresaMagic />);
      }).not.toThrow();
    });
  });

});
