'use client';

import React, { useCallback, useRef, forwardRef, useState } from 'react';
import { motion, useMotionTemplate, useMotionValue } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useResponsive } from './enhanced-responsive';
import { useAccessibilityEnhanced } from './enhanced-accessibility';

interface EnhancedMagicCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
  gradientSize?: number;
  gradientColor?: string;
  gradientOpacity?: number;
  
  // Acessibilidade
  ariaLabel?: string;
  ariaDescribedBy?: string;
  role?: string;
  
  // Responsividade
  responsiveGradient?: boolean;
  disableEffectsOnMobile?: boolean;
  
  // Interação
  interactive?: boolean;
  focusable?: boolean;
  onClick?: () => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  
  // Performance
  reduceMotion?: boolean;
  lazyEffects?: boolean;
}

export const EnhancedMagicCard = forwardRef<HTMLDivElement, EnhancedMagicCardProps>(({
  children,
  className,
  gradientSize = 200,
  gradientColor = "#262626",
  gradientOpacity = 0.8,
  ariaLabel,
  ariaDescribedBy,
  role,
  responsiveGradient = true,
  disableEffectsOnMobile = true,
  interactive = false,
  focusable = false,
  onClick,
  onKeyDown,
  reduceMotion,
  lazyEffects = true,
  ...props
}, ref) => {
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  const cardRef = useRef<HTMLDivElement>(null);
  const [isInView, setIsInView] = useState(!lazyEffects);
  const [isFocused, setIsFocused] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // Hooks de responsividade e acessibilidade
  const { breakpoint, isTouch, capabilities } = useResponsive();
  const { state: accessibilityState } = useAccessibilityEnhanced();

  // Determinar se deve usar efeitos
  const shouldUseEffects = !accessibilityState.settings.reducedMotion && 
                          !reduceMotion && 
                          !(disableEffectsOnMobile && (breakpoint === 'xs' || breakpoint === 'sm')) &&
                          capabilities.deviceMemory >= 2;

  // Ajustar tamanho do gradiente baseado no breakpoint
  const responsiveGradientSize = responsiveGradient ? {
    xs: gradientSize * 0.5,
    sm: gradientSize * 0.7,
    md: gradientSize * 0.8,
    lg: gradientSize,
    xl: gradientSize * 1.2,
    '2xl': gradientSize * 1.4
  }[breakpoint] : gradientSize;

  // Intersection Observer para lazy effects
  React.useEffect(() => {
    if (!lazyEffects || isInView) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (cardRef.current) {
      observer.observe(cardRef.current);
    }

    return () => observer.disconnect();
  }, [lazyEffects, isInView]);

  const handleMouseMove = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      if (!shouldUseEffects || !isInView) return;

      if (cardRef.current) {
        const { left, top } = cardRef.current.getBoundingClientRect();
        mouseX.set(e.clientX - left);
        mouseY.set(e.clientY - top);
      }
    },
    [mouseX, mouseY, shouldUseEffects, isInView]
  );

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  const handleFocus = useCallback(() => {
    setIsFocused(true);
  }, []);

  const handleBlur = useCallback(() => {
    setIsFocused(false);
  }, []);

  const handleClick = useCallback(() => {
    if (onClick) {
      // Delay para evitar cliques acidentais se configurado
      const delay = accessibilityState.settings.clickDelay;
      if (delay > 0) {
        setTimeout(onClick, delay);
      } else {
        onClick();
      }
    }
  }, [onClick, accessibilityState.settings.clickDelay]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (onKeyDown) {
      onKeyDown(e);
    }
    
    // Ativar com Enter ou Space se for interativo
    if (interactive && (e.key === 'Enter' || e.key === ' ')) {
      e.preventDefault();
      handleClick();
    }
  }, [onKeyDown, interactive, handleClick]);

  const maskImage = useMotionTemplate`radial-gradient(${responsiveGradientSize}px at ${mouseX}px ${mouseY}px, white, transparent)`;
  
  const style = shouldUseEffects && isInView ? {
    maskImage,
    WebkitMaskImage: maskImage,
  } : {};

  // Classes dinâmicas baseadas no estado
  const dynamicClasses = cn(
    // Base
    "group relative flex size-full overflow-hidden rounded-xl border border-white/10 bg-gradient-to-br from-white/20 to-white/5 text-white shadow-2xl",
    
    // Padding responsivo
    {
      'p-3': breakpoint === 'xs',
      'p-4': breakpoint === 'sm',
      'p-5': breakpoint === 'md',
      'p-6': breakpoint === 'lg' || breakpoint === 'xl' || breakpoint === '2xl'
    },
    
    // Interatividade
    {
      'cursor-pointer': interactive || onClick,
      'focus:outline-none': focusable,
      'focus:ring-2 focus:ring-blue-500 focus:ring-offset-2': focusable && accessibilityState.settings.focusIndicators !== 'subtle',
      'focus:ring-1 focus:ring-blue-300': focusable && accessibilityState.settings.focusIndicators === 'subtle',
      'focus:ring-4 focus:ring-blue-600': focusable && accessibilityState.settings.focusIndicators === 'high-contrast'
    },
    
    // Estados visuais
    {
      'transform transition-transform duration-200': shouldUseEffects,
      'hover:scale-105': shouldUseEffects && !isTouch && interactive,
      'active:scale-95': shouldUseEffects && interactive,
      'ring-2 ring-blue-400': isFocused && focusable,
      'brightness-110': isHovered && shouldUseEffects
    },
    
    // Alto contraste
    {
      'border-white/30 bg-black text-white': accessibilityState.settings.highContrast,
      'shadow-xl': accessibilityState.settings.highContrast
    },
    
    className
  );

  // Propriedades de acessibilidade
  const accessibilityProps = {
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedBy,
    role: role || (interactive ? 'button' : undefined),
    tabIndex: focusable ? 0 : undefined,
    'aria-pressed': interactive && isHovered ? true : undefined
  };

  return (
    <motion.div
      ref={(node) => {
        cardRef.current = node;
        if (typeof ref === 'function') {
          ref(node);
        } else if (ref) {
          ref.current = node;
        }
      }}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onFocus={handleFocus}
      onBlur={handleBlur}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      className={dynamicClasses}
      initial={shouldUseEffects ? { opacity: 0, y: 20 } : { opacity: 1 }}
      animate={shouldUseEffects ? { opacity: 1, y: 0 } : { opacity: 1 }}
      transition={shouldUseEffects ? { 
        duration: accessibilityState.settings.animationDuration === 'fast' ? 0.2 : 
                 accessibilityState.settings.animationDuration === 'slow' ? 0.6 : 0.3 
      } : { duration: 0 }}
      whileHover={shouldUseEffects && !isTouch ? { scale: 1.02 } : {}}
      whileTap={shouldUseEffects ? { scale: 0.98 } : {}}
      {...accessibilityProps}
      {...props}
    >
      {/* Conteúdo principal */}
      <div className="relative z-10 w-full">
        {children}
      </div>

      {/* Efeito de gradiente */}
      {shouldUseEffects && isInView && (
        <motion.div
          className="pointer-events-none absolute -inset-px rounded-xl opacity-0 transition duration-300 group-hover:opacity-100"
          style={{
            ...style,
            background: `radial-gradient(${responsiveGradientSize}px circle at var(--mouse-x) var(--mouse-y), ${gradientColor}, transparent 40%)`,
            opacity: gradientOpacity,
          }}
          animate={{
            opacity: isHovered || isFocused ? gradientOpacity : 0
          }}
          transition={{
            duration: accessibilityState.settings.animationDuration === 'fast' ? 0.15 : 
                     accessibilityState.settings.animationDuration === 'slow' ? 0.6 : 0.3
          }}
        />
      )}

      {/* Indicador de foco para alto contraste */}
      {isFocused && accessibilityState.settings.highContrast && (
        <div className="absolute inset-0 border-4 border-yellow-400 rounded-xl pointer-events-none" />
      )}

      {/* Indicador de loading para lazy effects */}
      {lazyEffects && !isInView && (
        <div className="absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse rounded-xl" />
      )}
    </motion.div>
  );
});

EnhancedMagicCard.displayName = 'EnhancedMagicCard';

// Variantes pré-configuradas
export const InteractiveMagicCard = forwardRef<HTMLDivElement, Omit<EnhancedMagicCardProps, 'interactive' | 'focusable'>>((props, ref) => (
  <EnhancedMagicCard ref={ref} interactive focusable {...props} />
));

InteractiveMagicCard.displayName = 'InteractiveMagicCard';

export const AccessibleMagicCard = forwardRef<HTMLDivElement, Omit<EnhancedMagicCardProps, 'focusable' | 'disableEffectsOnMobile'>>((props, ref) => (
  <EnhancedMagicCard ref={ref} focusable disableEffectsOnMobile {...props} />
));

AccessibleMagicCard.displayName = 'AccessibleMagicCard';

export const PerformantMagicCard = forwardRef<HTMLDivElement, Omit<EnhancedMagicCardProps, 'lazyEffects' | 'responsiveGradient'>>((props, ref) => (
  <EnhancedMagicCard ref={ref} lazyEffects responsiveGradient {...props} />
));

PerformantMagicCard.displayName = 'PerformantMagicCard';
