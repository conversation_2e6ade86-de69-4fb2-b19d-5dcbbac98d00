'use client';

import { useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useApiCache } from './useApiCache';


interface DadosEmpresa {
  empresa_id: number;
  nome_empresa: string;
  cnpj?: string;
  endereco_completo?: string;
  telefone?: string;
  email?: string;
  segmento?: string;
  descricao?: string;
  logo_url?: string;
  imagem_capa_url?: string;
  slug: string;
  status: 'ativo' | 'inativo' | 'pendente';
  horario_funcionamento?: any;
  stripe_customer_id?: string;
  created_at: string;
  updated_at: string;
}

interface PlanoSaas {
  plano_id: number;
  nome_plano: string;
  preco_mensal: number;
  limite_servicos: number;
  limite_colaboradores: number;
  recursos_premium: boolean;
  status_assinatura: 'ativa' | 'cancelada' | 'pausada' | 'expirada';
  data_inicio: string;
  data_fim?: string;
  stripe_subscription_id?: string;
}

interface MetricasEmpresa {
  total_agendamentos_mes: number;
  receita_bruta_mes: number;
  receita_liquida_mes: number;
  total_clientes_ativos: number;
  total_servicos_ativos: number;
  total_colaboradores_ativos: number;
  taxa_confirmacao_mes: number;
  crescimento_receita_percentual: number;
  agendamentos_pendentes: number;
  proximos_vencimentos: number;
}

interface StatusConfiguracao {
  empresa_configurada: boolean;
  stripe_configurado: boolean;
  servicos_cadastrados: boolean;
  horarios_definidos: boolean;
  colaboradores_ativos: boolean;
  percentual_conclusao: number;
  proximos_passos: string[];
}



export function useEmpresaProprietario() {
  const { user } = useAuth();

  // Usar cache de API para dados da empresa
  const {
    data: dadosEmpresa,
    loading,
    error,
    refetch: buscarDadosEmpresa,
    mutate,
    invalidate
  } = useApiCache(
    `empresa-proprietario-${user?.id}`,
    async () => {
      if (!user) throw new Error('Usuário não autenticado');

      const response = await fetch('/api/proprietario/dashboard/empresa', {
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' }
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error ?? 'Erro ao buscar dados da empresa');
      }

      return result.data;
    },
    {
      ttl: 5 * 60 * 1000, // 5 minutos
      staleWhileRevalidate: true
    }
  );

  // Derivar estados dos dados
  const empresa = dadosEmpresa?.empresa ?? null;
  const planoSaas = dadosEmpresa?.planoSaas ?? null;
  const metricas = dadosEmpresa?.metricas ?? null;
  const statusConfiguracao = dadosEmpresa?.statusConfiguracao ?? null;
  const temEmpresa = !!empresa;
  const empresaAtiva = empresa?.status === 'ativo';

  // Atualizar dados da empresa
  const atualizarEmpresa = useCallback(async (dadosAtualizados: Partial<DadosEmpresa>) => {
    if (!empresa) return false;

    try {
      const response = await fetch(`/api/empresas/${empresa.empresa_id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dadosAtualizados),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error ?? 'Erro ao atualizar empresa');
      }

      // Atualizar cache local
      if (dadosEmpresa) {
        mutate({
          ...dadosEmpresa,
          empresa: { ...empresa, ...dadosAtualizados }
        });
      }

      return true;

    } catch (error: any) {
      throw new Error(error.message);
    }
  }, [empresa, dadosEmpresa, mutate]);

  // Limpar erro e invalidar cache
  const limparErro = useCallback(() => {
    invalidate();
  }, [invalidate]);

  // Verificar se precisa de configuração inicial
  const precisaConfiguracaoInicial = useCallback(() => {
    if (!statusConfiguracao) return true;
    return statusConfiguracao.percentual_conclusao < 100;
  }, [statusConfiguracao]);

  // Obter próximos passos
  const obterProximosPassos = useCallback(() => {
    return statusConfiguracao?.proximos_passos ?? [];
  }, [statusConfiguracao]);

  // Verificar se pode receber pagamentos
  const podeReceberPagamentos = useCallback(() => {
    return !!empresa?.stripe_customer_id;
  }, [empresa]);

  return {
    // Estado
    empresa,
    planoSaas,
    metricas,
    statusConfiguracao,
    loading,
    error,
    temEmpresa,
    empresaAtiva,

    // Ações
    buscarDadosEmpresa,
    atualizarEmpresa,
    limparErro,

    // Utilitários
    precisaConfiguracaoInicial,
    obterProximosPassos,
    podeReceberPagamentos
  };
}
