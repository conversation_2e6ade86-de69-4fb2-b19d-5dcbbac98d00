'use client';

import React, { memo, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MagicCard } from './magic-card';
import { ShimmerButton } from './shimmer-button';
import { useAccessibility, useKeyboardShortcuts } from '@/hooks/useAccessibility';

interface AccessibilityPanelProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

const AccessibilityPanel = memo(({
  isOpen,
  onClose,
  className = ''
}: Readonly<AccessibilityPanelProps>) => {
  const { settings, updateSetting, resetSettings, announceToScreenReader } = useAccessibility();
  useKeyboardShortcuts();

  const handleSettingChange = (key: keyof typeof settings, value: any) => {
    updateSetting(key, value);
    announceToScreenReader(`Configuração ${key} alterada para ${value}`);
  };

  const handleReset = () => {
    resetSettings();
    announceToScreenReader('Configurações de acessibilidade resetadas');
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 flex items-center justify-center p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        role="dialog"
        aria-modal="true"
        aria-labelledby="accessibility-title"
        aria-describedby="accessibility-description"
      >
        {/* Backdrop */}
        <motion.div
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        />

        {/* Panel */}
        <motion.div
          className={`relative z-10 w-full max-w-2xl max-h-[90vh] overflow-y-auto ${className}`}
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
        >
          <MagicCard className="p-6" gradientColor="#6366F1">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 
                  id="accessibility-title"
                  className="text-2xl font-bold text-gray-900 dark:text-white"
                >
                  ⚙️ Configurações de Acessibilidade
                </h2>
                <p 
                  id="accessibility-description"
                  className="text-sm text-gray-600 dark:text-gray-400 mt-1"
                >
                  Personalize a interface para suas necessidades
                </p>
              </div>
              
              <button
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 text-2xl"
                aria-label="Fechar painel de acessibilidade"
              >
                ✕
              </button>
            </div>

            {/* Configurações */}
            <div className="space-y-6">
              {/* Tema */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  🎨 Tema da Interface
                </label>
                <div className="grid grid-cols-3 gap-2">
                  {(['light', 'dark', 'auto'] as const).map((theme) => (
                    <button
                      key={theme}
                      onClick={() => handleSettingChange('theme', theme)}
                      className={`p-3 rounded-lg border text-sm font-medium transition-colors ${
                        settings.theme === theme
                          ? 'bg-blue-100 border-blue-500 text-blue-700 dark:bg-blue-900 dark:border-blue-400 dark:text-blue-300'
                          : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-700'
                      }`}
                      aria-pressed={settings.theme === theme}
                    >
                      {theme === 'light' && '☀️ Claro'}
                      {theme === 'dark' && '🌙 Escuro'}
                      {theme === 'auto' && '🔄 Automático'}
                    </button>
                  ))}
                </div>
              </div>

              {/* Tamanho da Fonte */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  📝 Tamanho da Fonte
                </label>
                <div className="grid grid-cols-4 gap-2">
                  {(['small', 'medium', 'large', 'extra-large'] as const).map((size) => (
                    <button
                      key={size}
                      onClick={() => handleSettingChange('fontSize', size)}
                      className={`p-3 rounded-lg border text-sm font-medium transition-colors ${
                        settings.fontSize === size
                          ? 'bg-green-100 border-green-500 text-green-700 dark:bg-green-900 dark:border-green-400 dark:text-green-300'
                          : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-700'
                      }`}
                      aria-pressed={settings.fontSize === size}
                    >
                      {size === 'small' && 'Pequena'}
                      {size === 'medium' && 'Média'}
                      {size === 'large' && 'Grande'}
                      {size === 'extra-large' && 'Extra'}
                    </button>
                  ))}
                </div>
              </div>

              {/* Switches */}
              <div className="space-y-4">
                {/* Movimento Reduzido */}
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      🎭 Reduzir Animações
                    </label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Minimiza movimentos e transições
                    </p>
                  </div>
                  <button
                    onClick={() => handleSettingChange('reducedMotion', !settings.reducedMotion)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      settings.reducedMotion ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
                    }`}
                    role="switch"
                    aria-checked={settings.reducedMotion}
                    aria-label="Alternar movimento reduzido"
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.reducedMotion ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                {/* Alto Contraste */}
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      🔆 Alto Contraste
                    </label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Aumenta o contraste das cores
                    </p>
                  </div>
                  <button
                    onClick={() => handleSettingChange('highContrast', !settings.highContrast)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      settings.highContrast ? 'bg-purple-600' : 'bg-gray-200 dark:bg-gray-700'
                    }`}
                    role="switch"
                    aria-checked={settings.highContrast}
                    aria-label="Alternar alto contraste"
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.highContrast ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                {/* Navegação por Teclado */}
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      ⌨️ Navegação por Teclado
                    </label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Destaca elementos focados pelo teclado
                    </p>
                  </div>
                  <button
                    onClick={() => handleSettingChange('keyboardNavigation', !settings.keyboardNavigation)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      settings.keyboardNavigation ? 'bg-green-600' : 'bg-gray-200 dark:bg-gray-700'
                    }`}
                    role="switch"
                    aria-checked={settings.keyboardNavigation}
                    aria-label="Alternar navegação por teclado"
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.keyboardNavigation ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              </div>

              {/* Atalhos de Teclado */}
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  ⌨️ Atalhos de Teclado
                </h3>
                <div className="space-y-2 text-xs text-gray-600 dark:text-gray-400">
                  <div className="flex justify-between">
                    <span>Alt + 1</span>
                    <span>Pular para conteúdo principal</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Alt + 2</span>
                    <span>Focar na navegação</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Alt + 3</span>
                    <span>Focar na busca</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Escape</span>
                    <span>Fechar modais</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tab</span>
                    <span>Navegar entre elementos</span>
                  </div>
                </div>
              </div>

              {/* Ações */}
              <div className="flex space-x-3 pt-4">
                <ShimmerButton
                  onClick={handleReset}
                  className="flex-1 text-sm py-3"
                  shimmerColor="#EF4444"
                  background="#EF444420"
                >
                  🔄 Resetar Configurações
                </ShimmerButton>
                
                <ShimmerButton
                  onClick={onClose}
                  className="flex-1 text-sm py-3"
                  shimmerColor="#10B981"
                  background="#10B98120"
                >
                  ✅ Salvar e Fechar
                </ShimmerButton>
              </div>
            </div>
          </MagicCard>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
});

AccessibilityPanel.displayName = 'AccessibilityPanel';

// Botão flutuante para abrir o painel
interface AccessibilityButtonProps {
  onClick: () => void;
  className?: string;
}

export const AccessibilityButton = memo(({
  onClick,
  className = ''
}: Readonly<AccessibilityButtonProps>) => {
  return (
    <motion.button
      onClick={onClick}
      className={`fixed bottom-4 left-4 w-12 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg z-40 flex items-center justify-center ${className}`}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      aria-label="Abrir configurações de acessibilidade"
      title="Configurações de Acessibilidade (Alt + A)"
    >
      <span className="text-lg">♿</span>
    </motion.button>
  );
});

AccessibilityButton.displayName = 'AccessibilityButton';

export { AccessibilityPanel };
