'use client';

import React, { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Tipos para configurações de acessibilidade
interface AccessibilitySettings {
  // Movimento e animações
  reducedMotion: boolean;
  animationDuration: 'fast' | 'normal' | 'slow' | 'none';
  
  // Visual
  highContrast: boolean;
  fontSize: 'small' | 'medium' | 'large' | 'extra-large';
  lineHeight: 'normal' | 'relaxed' | 'loose';
  letterSpacing: 'normal' | 'wide' | 'wider';
  
  // Cores e temas
  colorScheme: 'auto' | 'light' | 'dark' | 'high-contrast';
  colorBlindnessMode: 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia' | 'monochrome';
  
  // Navegação
  keyboardNavigation: boolean;
  focusIndicators: 'subtle' | 'prominent' | 'high-contrast';
  skipLinks: boolean;
  
  // Leitores de tela
  screenReaderOptimized: boolean;
  verboseDescriptions: boolean;
  announceChanges: boolean;
  
  // Interação
  clickDelay: number; // ms para evitar cliques acidentais
  hoverDelay: number; // ms para hover
  autoplay: boolean;
  
  // Cognitivo
  simplifiedInterface: boolean;
  hideComplexAnimations: boolean;
  showProgressIndicators: boolean;
}

interface AccessibilityState {
  settings: AccessibilitySettings;
  isKeyboardUser: boolean;
  lastInteractionType: 'mouse' | 'keyboard' | 'touch' | 'unknown';
  announcements: string[];
}

// Configurações padrão
const DEFAULT_SETTINGS: AccessibilitySettings = {
  reducedMotion: false,
  animationDuration: 'normal',
  highContrast: false,
  fontSize: 'medium',
  lineHeight: 'normal',
  letterSpacing: 'normal',
  colorScheme: 'auto',
  colorBlindnessMode: 'none',
  keyboardNavigation: true,
  focusIndicators: 'subtle',
  skipLinks: true,
  screenReaderOptimized: false,
  verboseDescriptions: false,
  announceChanges: true,
  clickDelay: 0,
  hoverDelay: 300,
  autoplay: true,
  simplifiedInterface: false,
  hideComplexAnimations: false,
  showProgressIndicators: true
};

// Context
const AccessibilityContext = createContext<{
  state: AccessibilityState;
  updateSetting: (key: keyof AccessibilitySettings, value: any) => void;
  resetSettings: () => void;
  announce: (message: string, priority?: 'polite' | 'assertive') => void;
  focusElement: (selector: string) => void;
  trapFocus: (container: HTMLElement) => () => void;
} | null>(null);

// Provider de acessibilidade
interface AccessibilityProviderProps {
  children: React.ReactNode;
  persistSettings?: boolean;
}

export const AccessibilityProvider: React.FC<AccessibilityProviderProps> = ({
  children,
  persistSettings = true
}) => {
  const [state, setState] = useState<AccessibilityState>(() => {
    const savedSettings = persistSettings && typeof window !== 'undefined' 
      ? localStorage.getItem('accessibility-settings')
      : null;
    
    const settings = savedSettings 
      ? { ...DEFAULT_SETTINGS, ...JSON.parse(savedSettings) }
      : { ...DEFAULT_SETTINGS };

    // Detectar preferências do sistema
    if (typeof window !== 'undefined') {
      if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        settings.reducedMotion = true;
      }
      if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
        settings.colorScheme = 'dark';
      }
      if (window.matchMedia('(prefers-contrast: more)').matches) {
        settings.highContrast = true;
      }
    }

    return {
      settings,
      isKeyboardUser: false,
      lastInteractionType: 'unknown',
      announcements: []
    };
  });

  const announcementRef = useRef<HTMLDivElement>(null);

  // Detectar tipo de interação
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleMouseDown = () => {
      setState(prev => ({ 
        ...prev, 
        lastInteractionType: 'mouse',
        isKeyboardUser: false 
      }));
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        setState(prev => ({ 
          ...prev, 
          lastInteractionType: 'keyboard',
          isKeyboardUser: true 
        }));
      }
    };

    const handleTouchStart = () => {
      setState(prev => ({ 
        ...prev, 
        lastInteractionType: 'touch',
        isKeyboardUser: false 
      }));
    };

    document.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('touchstart', handleTouchStart);

    return () => {
      document.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('touchstart', handleTouchStart);
    };
  }, []);

  // Aplicar configurações CSS
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const root = document.documentElement;
    const { settings } = state;

    // Tamanho da fonte
    const fontSizes = {
      small: '14px',
      medium: '16px',
      large: '18px',
      'extra-large': '20px'
    };
    root.style.setProperty('--base-font-size', fontSizes[settings.fontSize]);

    // Altura da linha
    const lineHeights = {
      normal: '1.5',
      relaxed: '1.625',
      loose: '1.75'
    };
    root.style.setProperty('--base-line-height', lineHeights[settings.lineHeight]);

    // Espaçamento entre letras
    const letterSpacings = {
      normal: '0',
      wide: '0.025em',
      wider: '0.05em'
    };
    root.style.setProperty('--base-letter-spacing', letterSpacings[settings.letterSpacing]);

    // Duração das animações
    const durations = {
      fast: '0.15s',
      normal: '0.3s',
      slow: '0.6s',
      none: '0s'
    };
    root.style.setProperty('--animation-duration', durations[settings.animationDuration]);

    // Alto contraste
    if (settings.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }

    // Movimento reduzido
    if (settings.reducedMotion) {
      root.classList.add('reduce-motion');
    } else {
      root.classList.remove('reduce-motion');
    }

    // Esquema de cores
    if (settings.colorScheme !== 'auto') {
      root.setAttribute('data-theme', settings.colorScheme);
    } else {
      root.removeAttribute('data-theme');
    }

    // Daltonismo
    if (settings.colorBlindnessMode !== 'none') {
      root.setAttribute('data-colorblind', settings.colorBlindnessMode);
    } else {
      root.removeAttribute('data-colorblind');
    }

    // Indicadores de foco
    root.setAttribute('data-focus-style', settings.focusIndicators);

    // Interface simplificada
    if (settings.simplifiedInterface) {
      root.classList.add('simplified-interface');
    } else {
      root.classList.remove('simplified-interface');
    }

  }, [state.settings]);

  // Salvar configurações
  useEffect(() => {
    if (persistSettings && typeof window !== 'undefined') {
      localStorage.setItem('accessibility-settings', JSON.stringify(state.settings));
    }
  }, [state.settings, persistSettings]);

  const updateSetting = useCallback((key: keyof AccessibilitySettings, value: any) => {
    setState(prev => ({
      ...prev,
      settings: {
        ...prev.settings,
        [key]: value
      }
    }));
  }, []);

  const resetSettings = useCallback(() => {
    setState(prev => ({
      ...prev,
      settings: { ...DEFAULT_SETTINGS }
    }));
    
    if (persistSettings && typeof window !== 'undefined') {
      localStorage.removeItem('accessibility-settings');
    }
  }, [persistSettings]);

  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (!state.settings.announceChanges) return;

    setState(prev => ({
      ...prev,
      announcements: [...prev.announcements, message]
    }));

    // Limpar anúncio após um tempo
    setTimeout(() => {
      setState(prev => ({
        ...prev,
        announcements: prev.announcements.filter(a => a !== message)
      }));
    }, 3000);

    // Anunciar para leitores de tela
    if (announcementRef.current) {
      announcementRef.current.setAttribute('aria-live', priority);
      announcementRef.current.textContent = message;
      
      setTimeout(() => {
        if (announcementRef.current) {
          announcementRef.current.textContent = '';
        }
      }, 100);
    }
  }, [state.settings.announceChanges]);

  const focusElement = useCallback((selector: string) => {
    const element = document.querySelector(selector) as HTMLElement;
    if (element) {
      element.focus();
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, []);

  const trapFocus = useCallback((container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      }
      
      if (e.key === 'Escape') {
        container.focus();
      }
    };

    container.addEventListener('keydown', handleKeyDown);
    firstElement?.focus();

    return () => {
      container.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return (
    <AccessibilityContext.Provider value={{
      state,
      updateSetting,
      resetSettings,
      announce,
      focusElement,
      trapFocus
    }}>
      {children}
      
      {/* Região para anúncios de leitores de tela */}
      <div
        ref={announcementRef}
        className="sr-only"
        aria-live="polite"
        aria-atomic="true"
        role="status"
      />
      
      {/* Anúncios visuais */}
      <AnimatePresence>
        {state.announcements.map((announcement, index) => (
          <motion.div
            key={`${announcement}-${index}`}
            className="fixed top-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 max-w-sm"
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 100 }}
            transition={{ duration: 0.3 }}
          >
            {announcement}
          </motion.div>
        ))}
      </AnimatePresence>
    </AccessibilityContext.Provider>
  );
};

// Hook para usar acessibilidade
export const useAccessibilityEnhanced = () => {
  const context = useContext(AccessibilityContext);
  if (!context) {
    throw new Error('useAccessibilityEnhanced must be used within AccessibilityProvider');
  }
  return context;
};

// Hook para atalhos de teclado avançados
export const useKeyboardShortcuts = (shortcuts: Record<string, () => void>) => {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const key = `${e.ctrlKey ? 'ctrl+' : ''}${e.altKey ? 'alt+' : ''}${e.shiftKey ? 'shift+' : ''}${e.key.toLowerCase()}`;
      
      if (shortcuts[key]) {
        e.preventDefault();
        shortcuts[key]();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [shortcuts]);
};

// Componente para skip links
interface SkipLinksProps {
  links?: Array<{ href: string; label: string }>;
}

export const SkipLinks: React.FC<SkipLinksProps> = ({
  links = [
    { href: '#main-content', label: 'Pular para conteúdo principal' },
    { href: '#navigation', label: 'Pular para navegação' },
    { href: '#footer', label: 'Pular para rodapé' }
  ]
}) => {
  const { state } = useAccessibilityEnhanced();
  
  if (!state.settings.skipLinks) return null;

  return (
    <nav aria-label="Links de navegação rápida" className="sr-only focus-within:not-sr-only">
      <ul className="fixed top-0 left-0 z-50 flex flex-col bg-blue-600 text-white">
        {links.map((link, index) => (
          <li key={index}>
            <a
              href={link.href}
              className="block px-4 py-2 text-sm font-medium hover:bg-blue-700 focus:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600"
              onClick={(e) => {
                e.preventDefault();
                const target = document.querySelector(link.href);
                if (target) {
                  (target as HTMLElement).focus();
                  target.scrollIntoView({ behavior: 'smooth' });
                }
              }}
            >
              {link.label}
            </a>
          </li>
        ))}
      </ul>
    </nav>
  );
};
