'use client';

import React, { memo, useState } from 'react';
import { motion } from 'framer-motion';
import { MagicCard } from '@/components/ui/magic-card';
import { TextAnimate } from '@/components/ui/text-animate';
import { ShimmerButton } from '@/components/ui/shimmer-button';
import { NumberTicker } from '@/components/ui/number-ticker';
import { useEmpresaProprietario } from '@/hooks/useEmpresaProprietario';

interface InfoCardProps {
  title: string;
  value: string | number;
  icon: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  delay?: number;
  format?: 'text' | 'number' | 'currency' | 'percentage';
}

const InfoCard = memo(({
  title,
  value,
  icon,
  description,
  action,
  delay = 0,
  format = 'text'
}: Readonly<InfoCardProps>) => {
  const formatValue = (val: string | number) => {
    if (typeof val === 'string') return val;
    
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL'
        }).format(val);
      case 'percentage':
        return `${val}%`;
      case 'number':
        return <NumberTicker value={val} />;
      default:
        return val;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay, duration: 0.5 }}
      whileHover={{ scale: 1.02 }}
    >
      <MagicCard className="p-4 h-full" gradientColor="#3B82F6" gradientSize={150}>
        <div className="flex items-start justify-between mb-3">
          <motion.div
            className="text-2xl"
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ duration: 0.2 }}
          >
            {icon}
          </motion.div>
          
          {action && (
            <ShimmerButton
              onClick={action.onClick}
              className="text-xs px-2 py-1"
              shimmerColor="#3B82F6"
              background="#3B82F620"
            >
              {action.label}
            </ShimmerButton>
          )}
        </div>

        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">
            {title}
          </h3>
          
          <div className="text-lg font-bold text-gray-900 dark:text-white">
            {formatValue(value)}
          </div>
          
          {description && (
            <p className="text-xs text-gray-500 dark:text-gray-500">
              {description}
            </p>
          )}
        </div>
      </MagicCard>
    </motion.div>
  );
});

InfoCard.displayName = 'InfoCard';

interface StatusBadgeProps {
  status: 'ativo' | 'inativo' | 'pendente' | 'suspenso';
  className?: string;
}

const StatusBadge = memo(({ status, className = '' }: Readonly<StatusBadgeProps>) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'ativo':
        return {
          color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
          icon: '✅',
          label: 'Ativo'
        };
      case 'inativo':
        return {
          color: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
          icon: '⏸️',
          label: 'Inativo'
        };
      case 'pendente':
        return {
          color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
          icon: '⏳',
          label: 'Pendente'
        };
      case 'suspenso':
        return {
          color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
          icon: '🚫',
          label: 'Suspenso'
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: '❓',
          label: 'Desconhecido'
        };
    }
  };

  const config = getStatusConfig();

  return (
    <motion.span
      className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${config.color} ${className}`}
      whileHover={{ scale: 1.05 }}
    >
      <span>{config.icon}</span>
      <span>{config.label}</span>
    </motion.span>
  );
});

StatusBadge.displayName = 'StatusBadge';

interface InformacoesEmpresaOrganizadaProps {
  className?: string;
}

const InformacoesEmpresaOrganizada = memo(({
  className = ''
}: Readonly<InformacoesEmpresaOrganizadaProps>) => {
  const {
    empresa,
    loading,
    error,
    temEmpresa,
    empresaAtiva,
    atualizarEmpresa
  } = useEmpresaProprietario();

  const [editMode, setEditMode] = useState(false);

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(8)].map((_, i) => (
            <MagicCard key={`loading-${i}`} className="animate-pulse p-4">
              <div className="space-y-3">
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </div>
            </MagicCard>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <MagicCard className="border-red-200 bg-red-50 dark:bg-red-900/20 p-6" gradientColor="#EF4444">
        <div className="text-center space-y-4">
          <div className="text-red-600 dark:text-red-400 text-4xl">⚠️</div>
          <div>
            <h3 className="font-semibold text-red-800 dark:text-red-200 text-lg">
              Erro ao carregar informações
            </h3>
            <p className="text-sm text-red-700 dark:text-red-300 mt-2">
              {error}
            </p>
          </div>
        </div>
      </MagicCard>
    );
  }

  if (!temEmpresa) {
    return (
      <MagicCard className="border-blue-200 bg-blue-50 dark:bg-blue-900/20 p-6" gradientColor="#3B82F6">
        <div className="text-center space-y-4">
          <div className="text-blue-600 dark:text-blue-400 text-4xl">🏢</div>
          <div>
            <h3 className="font-semibold text-blue-800 dark:text-blue-200 text-lg">
              Configure sua empresa
            </h3>
            <p className="text-sm text-blue-700 dark:text-blue-300 mt-2">
              Complete as informações da sua empresa para começar a usar o sistema.
            </p>
          </div>
          <ShimmerButton
            onClick={() => console.log('Configurar empresa')}
            className="px-6 py-2"
            shimmerColor="#3B82F6"
            background="#3B82F6"
          >
            Configurar Empresa
          </ShimmerButton>
        </div>
      </MagicCard>
    );
  }

  const infoCards = [
    {
      title: 'Nome da Empresa',
      value: empresa?.nome || 'Não informado',
      icon: '🏢',
      description: 'Razão social ou nome fantasia',
      action: {
        label: 'Editar',
        onClick: () => setEditMode(true)
      }
    },
    {
      title: 'Email Principal',
      value: empresa?.email || 'Não informado',
      icon: '📧',
      description: 'Email para comunicações importantes'
    },
    {
      title: 'Telefone',
      value: empresa?.telefone || 'Não informado',
      icon: '📞',
      description: 'Contato principal da empresa'
    },
    {
      title: 'Status',
      value: empresa?.status || 'inativo',
      icon: '📊',
      description: 'Status atual da empresa no sistema',
      format: 'text' as const
    },
    {
      title: 'Data de Cadastro',
      value: empresa?.created_at ? new Date(empresa.created_at).toLocaleDateString('pt-BR') : 'Não informado',
      icon: '📅',
      description: 'Data de registro no sistema'
    },
    {
      title: 'Última Atualização',
      value: empresa?.updated_at ? new Date(empresa.updated_at).toLocaleDateString('pt-BR') : 'Não informado',
      icon: '🔄',
      description: 'Última modificação dos dados'
    },
    {
      title: 'ID da Empresa',
      value: empresa?.empresa_id || 0,
      icon: '🆔',
      description: 'Identificador único no sistema',
      format: 'number' as const
    },
    {
      title: 'Configuração',
      value: empresaAtiva ? 'Completa' : 'Pendente',
      icon: '⚙️',
      description: 'Status da configuração inicial',
      action: {
        label: 'Configurar',
        onClick: () => console.log('Configurar empresa')
      }
    }
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header com status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <TextAnimate
            animation="blurInUp"
            className="text-xl font-semibold text-gray-900 dark:text-white"
          >
            {empresa?.nome || 'Empresa'}
          </TextAnimate>
          <StatusBadge status={empresa?.status as any || 'inativo'} />
        </div>
        
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          <ShimmerButton
            onClick={() => setEditMode(!editMode)}
            className="px-4 py-2"
            shimmerColor="#3B82F6"
            background="#3B82F620"
          >
            {editMode ? '💾 Salvar' : '✏️ Editar'}
          </ShimmerButton>
        </motion.div>
      </div>

      {/* Grid de informações */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {infoCards.map((card, index) => (
          <InfoCard
            key={card.title}
            {...card}
            delay={index * 0.1}
          />
        ))}
      </div>

      {/* Resumo rápido */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <MagicCard className="p-4" gradientColor="#10B981">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-2xl">📈</span>
              <div>
                <div className="font-medium text-white">
                  Empresa {empresaAtiva ? 'Ativa' : 'Inativa'}
                </div>
                <div className="text-sm text-white/80">
                  {empresaAtiva 
                    ? 'Todos os sistemas funcionando normalmente'
                    : 'Configure sua empresa para ativar todos os recursos'
                  }
                </div>
              </div>
            </div>
            
            <div className="text-right">
              <div className="text-2xl font-bold text-white">
                {empresaAtiva ? '100' : '0'}%
              </div>
              <div className="text-xs text-white/80">Configuração</div>
            </div>
          </div>
        </MagicCard>
      </motion.div>
    </div>
  );
});

InformacoesEmpresaOrganizada.displayName = 'InformacoesEmpresaOrganizada';

export { InformacoesEmpresaOrganizada };
