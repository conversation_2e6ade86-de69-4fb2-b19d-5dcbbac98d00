'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

export default function ProprietarioDashboardPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirecionar automaticamente para o dashboard Magic UI
    router.replace('/proprietario/dashboard-magic');
  }, [router]);

  return (
    <ProtectedRoute requiredRole="Proprietario">
      <div className="min-h-screen flex items-center justify-center bg-[var(--background)]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary)] mx-auto mb-4"></div>
          <p className="text-[var(--text-secondary)]">Redirecionando para o dashboard...</p>
        </div>
      </div>
    </ProtectedRoute>
  );
}



