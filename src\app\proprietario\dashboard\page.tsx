'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';

export default function ProprietarioDashboardPage() {
  const router = useRouter();
  const { user, loading, initialized } = useAuth();
  const [redirectTimeout, setRedirectTimeout] = useState(false);

  useEffect(() => {
    // Timeout para evitar loading infinito
    const timeout = setTimeout(() => {
      setRedirectTimeout(true);
    }, 5000); // 5 segundos

    return () => clearTimeout(timeout);
  }, []);

  useEffect(() => {
    // Só redirecionar quando a autenticação estiver inicializada
    if (initialized && !loading && user) {
      const timer = setTimeout(() => {
        router.replace('/proprietario/dashboard-magic');
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [router, initialized, loading, user]);

  // Se passou do timeout, mostrar opção manual
  if (redirectTimeout) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <div className="text-center max-w-md mx-auto p-8">
          <div className="text-6xl mb-6">🚀</div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Dashboard ServiceTech
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            O redirecionamento automático não funcionou. Clique no botão abaixo para acessar o dashboard.
          </p>
          <button
            onClick={() => router.push('/proprietario/dashboard-magic')}
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200"
          >
            Acessar Dashboard
          </button>

          {/* Debug info */}
          <div className="mt-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg text-sm text-left">
            <h3 className="font-semibold mb-2">Debug Info:</h3>
            <p>Loading: {loading ? 'true' : 'false'}</p>
            <p>Initialized: {initialized ? 'true' : 'false'}</p>
            <p>User: {user ? user.email : 'null'}</p>
            <p>Role: {user?.role || 'none'}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute requiredRole="Proprietario">
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Carregando Dashboard
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Redirecionando para o dashboard...
          </p>

          {/* Indicador de progresso */}
          <div className="mt-4 w-64 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mx-auto">
            <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}



