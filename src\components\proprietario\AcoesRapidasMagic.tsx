'use client';

import React, { memo, useCallback } from 'react';
import Link from 'next/link';
import { MagicCard } from '@/components/ui/magic-card';
import { TextAnimate } from '@/components/ui/text-animate';
import { RainbowButton } from '@/components/ui/rainbow-button';
import { ShinyButton } from '@/components/ui/shiny-button';
import { ShimmerButton } from '@/components/ui/shimmer-button';
import { InteractiveHoverButton } from '@/components/ui/interactive-hover-button';
import { motion } from 'framer-motion';
import { useAcoesRapidas } from '@/hooks/useAcoesRapidas';

interface AcaoRapidaProps {
  titulo: string;
  descricao: string;
  icone: string;
  href?: string;
  onClick?: () => void;
  tipo: 'primary' | 'secondary' | 'success' | 'warning' | 'rainbow' | 'shiny' | 'interactive';
  delay?: number;
  badge?: string;
  disabled?: boolean;
}

const AcaoRapida = memo(({
  titulo,
  descricao,
  icone,
  href,
  onClick,
  tipo,
  delay = 0,
  badge,
  disabled = false
}: Readonly<AcaoRapidaProps>) => {
  const renderButton = () => {
    const buttonContent = (
      <div className="flex items-center space-x-3 w-full">
        <motion.div 
          className="text-2xl"
          animate={{ rotate: [0, 10, -10, 0] }}
          transition={{ duration: 2, repeat: Infinity, repeatDelay: 4 }}
        >
          {icone}
        </motion.div>
        <div className="flex-1 text-left">
          <div className="font-semibold text-sm">{titulo}</div>
          <div className="text-xs opacity-80">{descricao}</div>
        </div>
        {badge && (
          <motion.div 
            className="bg-red-500 text-white text-xs px-2 py-1 rounded-full"
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            {badge}
          </motion.div>
        )}
      </div>
    );

    const buttonProps = {
      className: "w-full p-4 h-auto",
      disabled,
      onClick
    };

    switch (tipo) {
      case 'rainbow':
        return <RainbowButton {...buttonProps}>{buttonContent}</RainbowButton>;
      case 'shiny':
        return <ShinyButton {...buttonProps}>{buttonContent}</ShinyButton>;
      case 'interactive':
        return <InteractiveHoverButton {...buttonProps}>{buttonContent}</InteractiveHoverButton>;
      case 'primary':
        return (
          <ShimmerButton 
            {...buttonProps}
            shimmerColor="#3B82F6"
            background="rgba(59, 130, 246, 0.9)"
          >
            {buttonContent}
          </ShimmerButton>
        );
      case 'success':
        return (
          <ShimmerButton 
            {...buttonProps}
            shimmerColor="#10B981"
            background="rgba(16, 185, 129, 0.9)"
          >
            {buttonContent}
          </ShimmerButton>
        );
      case 'warning':
        return (
          <ShimmerButton 
            {...buttonProps}
            shimmerColor="#F59E0B"
            background="rgba(245, 158, 11, 0.9)"
          >
            {buttonContent}
          </ShimmerButton>
        );
      default:
        return (
          <ShimmerButton 
            {...buttonProps}
            shimmerColor="#6B7280"
            background="rgba(107, 114, 128, 0.9)"
          >
            {buttonContent}
          </ShimmerButton>
        );
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ delay, duration: 0.5 }}
      whileHover={{ scale: 1.02 }}
      className="relative"
    >
      {href ? (
        <Link href={href} className="block">
          {renderButton()}
        </Link>
      ) : (
        renderButton()
      )}
      
      {/* Efeito de brilho no hover */}
      <motion.div 
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 pointer-events-none"
        whileHover={{ opacity: 1, x: ['-100%', '100%'] }}
        transition={{ duration: 0.6 }}
      />
    </motion.div>
  );
});

interface AcoesRapidasMagicProps {
  className?: string;
}

const AcoesRapidasMagicComponent = ({ className = '' }: Readonly<AcoesRapidasMagicProps>) => {
  const {
    loading,
    error,
    agendamentosHoje,
    notificacoesPendentes,
    tarefasPendentes,
    executarAcao
  } = useAcoesRapidas();

  const handleBackup = useCallback(async () => {
    try {
      await executarAcao('backup');
    } catch (error) {
      console.error('Erro ao executar backup:', error);
    }
  }, [executarAcao]);

  if (loading) {
    return (
      <MagicCard className={`animate-pulse p-6 ${className}`} gradientColor="#3B82F6">
        <div className="space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/2"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={`loading-action-${i}`} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </MagicCard>
    );
  }

  if (error) {
    return (
      <MagicCard className={`border-red-200 bg-red-50 p-6 ${className}`} gradientColor="#EF4444">
        <div className="text-center space-y-4">
          <div className="text-red-600 text-4xl">⚡</div>
          <div>
            <h3 className="font-semibold text-red-800 text-lg">Erro ao carregar ações</h3>
            <p className="text-sm text-red-700 mt-2">
              Não foi possível carregar as ações rápidas.
            </p>
          </div>
        </div>
      </MagicCard>
    );
  }

  const acoesRapidas: AcaoRapidaProps[] = [
    {
      titulo: 'Novo Agendamento',
      descricao: 'Criar um novo agendamento',
      icone: '📅',
      href: '/proprietario/agendamentos/novo',
      tipo: 'rainbow',
      delay: 0
    },
    {
      titulo: 'Gerenciar Clientes',
      descricao: 'Ver e editar clientes',
      icone: '👥',
      href: '/proprietario/clientes',
      tipo: 'primary',
      delay: 0.1
    },
    {
      titulo: 'Relatórios',
      descricao: 'Visualizar relatórios',
      icone: '📊',
      href: '/proprietario/relatorios',
      tipo: 'success',
      delay: 0.2
    },
    {
      titulo: 'Configurações',
      descricao: 'Ajustar configurações',
      icone: '⚙️',
      href: '/proprietario/configuracoes',
      tipo: 'secondary',
      delay: 0.3
    },
    {
      titulo: 'Agendamentos Hoje',
      descricao: `${agendamentosHoje} agendamentos`,
      icone: '🗓️',
      href: '/proprietario/agendamentos?filtro=hoje',
      tipo: 'interactive',
      delay: 0.4,
      badge: agendamentosHoje > 0 ? agendamentosHoje.toString() : undefined
    },
    {
      titulo: 'Notificações',
      descricao: 'Ver notificações pendentes',
      icone: '🔔',
      href: '/proprietario/notificacoes',
      tipo: 'warning',
      delay: 0.5,
      badge: notificacoesPendentes > 0 ? notificacoesPendentes.toString() : undefined
    },
    {
      titulo: 'Backup de Dados',
      descricao: 'Fazer backup dos dados',
      icone: '💾',
      onClick: handleBackup,
      tipo: 'shiny',
      delay: 0.6
    },
    {
      titulo: 'Suporte',
      descricao: 'Entrar em contato',
      icone: '🆘',
      href: '/suporte',
      tipo: 'secondary',
      delay: 0.7
    }
  ];

  return (
    <div className={className}>
      {/* Header */}
      <div className="mb-6">
        <TextAnimate
          animation="slideRight"
          className="text-2xl font-bold text-gray-900 dark:text-white"
          by="word"
        >
          Ações Rápidas
        </TextAnimate>
        <motion.p 
          className="text-gray-600 dark:text-gray-400 mt-2"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          Acesse rapidamente as funcionalidades mais utilizadas
        </motion.p>
      </div>

      {/* Grid de ações */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {acoesRapidas.map((acao, index) => (
          <AcaoRapida
            key={acao.titulo}
            {...acao}
          />
        ))}
      </div>

      {/* Estatísticas rápidas */}
      <motion.div 
        className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <MagicCard className="p-4 text-center" gradientColor="#3B82F6">
          <div className="text-2xl mb-2">📅</div>
          <div className="text-lg font-bold text-white">{agendamentosHoje}</div>
          <div className="text-sm text-white/80">Agendamentos Hoje</div>
        </MagicCard>
        
        <MagicCard className="p-4 text-center" gradientColor="#F59E0B">
          <div className="text-2xl mb-2">🔔</div>
          <div className="text-lg font-bold text-white">{notificacoesPendentes}</div>
          <div className="text-sm text-white/80">Notificações</div>
        </MagicCard>
        
        <MagicCard className="p-4 text-center" gradientColor="#10B981">
          <div className="text-2xl mb-2">✅</div>
          <div className="text-lg font-bold text-white">{tarefasPendentes}</div>
          <div className="text-sm text-white/80">Tarefas Pendentes</div>
        </MagicCard>
      </motion.div>

      {/* Efeito de partículas de ação */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={`action-particle-${i}`}
            className="absolute w-1 h-1 bg-blue-400 rounded-full opacity-40"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -25, 0],
              x: [0, Math.random() * 15 - 7.5, 0],
              opacity: [0.4, 0.8, 0.4],
            }}
            transition={{
              duration: 4 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 3,
            }}
          />
        ))}
      </div>
    </div>
  );
};

// Memoizar componente para evitar re-renders desnecessários
export const AcoesRapidasMagic = memo(AcoesRapidasMagicComponent, (prevProps, nextProps) => {
  return prevProps.className === nextProps.className;
});
