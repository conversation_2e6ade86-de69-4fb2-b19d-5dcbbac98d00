'use client';

import React, { forwardRef, useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useResponsive } from './enhanced-responsive';
import { useAccessibilityEnhanced } from './enhanced-accessibility';

interface EnhancedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  
  // Variantes visuais
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'ghost' | 'outline';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  
  // Estados
  loading?: boolean;
  loadingText?: string;
  
  // Acessibilidade
  ariaLabel?: string;
  ariaDescribedBy?: string;
  ariaExpanded?: boolean;
  ariaControls?: string;
  
  // Ícones
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  
  // Comportamento
  fullWidth?: boolean;
  rippleEffect?: boolean;
  hapticFeedback?: boolean;
  
  // Tooltip
  tooltip?: string;
  tooltipPosition?: 'top' | 'bottom' | 'left' | 'right';
}

export const EnhancedButton = forwardRef<HTMLButtonElement, EnhancedButtonProps>(({
  children,
  className,
  variant = 'primary',
  size = 'md',
  loading = false,
  loadingText = 'Carregando...',
  ariaLabel,
  ariaDescribedBy,
  ariaExpanded,
  ariaControls,
  leftIcon,
  rightIcon,
  fullWidth = false,
  rippleEffect = true,
  hapticFeedback = false,
  tooltip,
  tooltipPosition = 'top',
  onClick,
  onKeyDown,
  disabled,
  ...props
}, ref) => {
  const [isPressed, setIsPressed] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number }>>([]);

  // Hooks
  const { breakpoint, isTouch, capabilities } = useResponsive();
  const { state: accessibilityState, announce } = useAccessibilityEnhanced();

  // Determinar se deve usar efeitos
  const shouldUseEffects = !accessibilityState.settings.reducedMotion && 
                          capabilities.deviceMemory >= 2;

  // Configurações de tamanho responsivo
  const responsiveSizes = {
    xs: {
      xs: 'px-2 py-1 text-xs',
      sm: 'px-2 py-1 text-xs',
      md: 'px-3 py-1.5 text-sm',
      lg: 'px-3 py-1.5 text-sm',
      xl: 'px-3 py-1.5 text-sm',
      '2xl': 'px-3 py-1.5 text-sm'
    },
    sm: {
      xs: 'px-3 py-1.5 text-sm',
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-sm',
      lg: 'px-4 py-2 text-sm',
      xl: 'px-4 py-2 text-sm',
      '2xl': 'px-4 py-2 text-sm'
    },
    md: {
      xs: 'px-4 py-2 text-sm',
      sm: 'px-4 py-2 text-sm',
      md: 'px-6 py-2.5 text-base',
      lg: 'px-6 py-2.5 text-base',
      xl: 'px-6 py-2.5 text-base',
      '2xl': 'px-6 py-2.5 text-base'
    },
    lg: {
      xs: 'px-6 py-2.5 text-base',
      sm: 'px-6 py-2.5 text-base',
      md: 'px-8 py-3 text-lg',
      lg: 'px-8 py-3 text-lg',
      xl: 'px-8 py-3 text-lg',
      '2xl': 'px-8 py-3 text-lg'
    },
    xl: {
      xs: 'px-8 py-3 text-lg',
      sm: 'px-8 py-3 text-lg',
      md: 'px-10 py-4 text-xl',
      lg: 'px-10 py-4 text-xl',
      xl: 'px-10 py-4 text-xl',
      '2xl': 'px-10 py-4 text-xl'
    }
  };

  // Variantes de cor
  const variants = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white border-blue-600 focus:ring-blue-500',
    secondary: 'bg-gray-600 hover:bg-gray-700 text-white border-gray-600 focus:ring-gray-500',
    success: 'bg-green-600 hover:bg-green-700 text-white border-green-600 focus:ring-green-500',
    danger: 'bg-red-600 hover:bg-red-700 text-white border-red-600 focus:ring-red-500',
    warning: 'bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-600 focus:ring-yellow-500',
    ghost: 'bg-transparent hover:bg-gray-100 text-gray-700 border-transparent focus:ring-gray-500',
    outline: 'bg-transparent hover:bg-gray-50 text-gray-700 border-gray-300 focus:ring-gray-500'
  };

  // Variantes para alto contraste
  const highContrastVariants = {
    primary: 'bg-black hover:bg-gray-800 text-white border-white focus:ring-yellow-400',
    secondary: 'bg-gray-800 hover:bg-gray-700 text-white border-white focus:ring-yellow-400',
    success: 'bg-green-800 hover:bg-green-700 text-white border-white focus:ring-yellow-400',
    danger: 'bg-red-800 hover:bg-red-700 text-white border-white focus:ring-yellow-400',
    warning: 'bg-yellow-800 hover:bg-yellow-700 text-black border-white focus:ring-yellow-400',
    ghost: 'bg-white hover:bg-gray-100 text-black border-black focus:ring-yellow-400',
    outline: 'bg-white hover:bg-gray-100 text-black border-black focus:ring-yellow-400'
  };

  const handleClick = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) return;

    // Haptic feedback
    if (hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(50);
    }

    // Ripple effect
    if (rippleEffect && shouldUseEffects) {
      const rect = e.currentTarget.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const id = Date.now();

      setRipples(prev => [...prev, { id, x, y }]);

      // Remove ripple after animation
      setTimeout(() => {
        setRipples(prev => prev.filter(ripple => ripple.id !== id));
      }, 600);
    }

    // Anunciar ação para leitores de tela
    if (accessibilityState.settings.announceChanges) {
      announce(`Botão ${ariaLabel || children} ativado`);
    }

    onClick?.(e);
  }, [disabled, loading, hapticFeedback, rippleEffect, shouldUseEffects, onClick, ariaLabel, children, announce, accessibilityState.settings.announceChanges]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLButtonElement>) => {
    if (e.key === ' ' || e.key === 'Enter') {
      setIsPressed(true);
    }
    onKeyDown?.(e);
  }, [onKeyDown]);

  const handleKeyUp = useCallback(() => {
    setIsPressed(false);
  }, []);

  const handleMouseDown = useCallback(() => {
    if (!isTouch) setIsPressed(true);
  }, [isTouch]);

  const handleMouseUp = useCallback(() => {
    setIsPressed(false);
  }, []);

  const handleMouseEnter = useCallback(() => {
    if (tooltip) {
      setTimeout(() => setShowTooltip(true), accessibilityState.settings.hoverDelay);
    }
  }, [tooltip, accessibilityState.settings.hoverDelay]);

  const handleMouseLeave = useCallback(() => {
    setShowTooltip(false);
  }, []);

  // Classes dinâmicas
  const buttonClasses = cn(
    // Base
    'relative inline-flex items-center justify-center font-medium rounded-lg border transition-all duration-200 focus:outline-none overflow-hidden',
    
    // Tamanho responsivo
    responsiveSizes[size][breakpoint],
    
    // Largura total
    fullWidth && 'w-full',
    
    // Variante de cor
    accessibilityState.settings.highContrast 
      ? highContrastVariants[variant]
      : variants[variant],
    
    // Estados
    {
      'opacity-50 cursor-not-allowed': disabled || loading,
      'cursor-pointer': !disabled && !loading,
      'transform scale-95': isPressed && shouldUseEffects,
    },
    
    // Foco
    {
      'focus:ring-2 focus:ring-offset-2': accessibilityState.settings.focusIndicators !== 'subtle',
      'focus:ring-1': accessibilityState.settings.focusIndicators === 'subtle',
      'focus:ring-4': accessibilityState.settings.focusIndicators === 'high-contrast',
    },
    
    // Touch targets maiores em dispositivos móveis
    isTouch && 'min-h-[44px] min-w-[44px]',
    
    className
  );

  // Propriedades de acessibilidade
  const accessibilityProps = {
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedBy,
    'aria-expanded': ariaExpanded,
    'aria-controls': ariaControls,
    'aria-pressed': isPressed,
    'aria-busy': loading,
    'aria-disabled': disabled || loading,
  };

  return (
    <div className="relative inline-block">
      <motion.button
        ref={ref}
        className={buttonClasses}
        disabled={disabled || loading}
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        onKeyUp={handleKeyUp}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        whileHover={shouldUseEffects && !isTouch ? { scale: 1.02 } : {}}
        whileTap={shouldUseEffects ? { scale: 0.98 } : {}}
        transition={{
          duration: accessibilityState.settings.animationDuration === 'fast' ? 0.1 :
                   accessibilityState.settings.animationDuration === 'slow' ? 0.4 : 0.2
        }}
        {...accessibilityProps}
        {...props}
      >
        {/* Ripple effects */}
        {ripples.map(ripple => (
          <motion.span
            key={ripple.id}
            className="absolute bg-white/30 rounded-full pointer-events-none"
            style={{
              left: ripple.x - 10,
              top: ripple.y - 10,
              width: 20,
              height: 20,
            }}
            initial={{ scale: 0, opacity: 1 }}
            animate={{ scale: 4, opacity: 0 }}
            transition={{ duration: 0.6 }}
          />
        ))}

        {/* Conteúdo do botão */}
        <span className="flex items-center justify-center space-x-2">
          {leftIcon && !loading && (
            <span className="flex-shrink-0">{leftIcon}</span>
          )}
          
          {loading && (
            <motion.span
              className="flex-shrink-0"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            >
              ⟳
            </motion.span>
          )}
          
          <span>
            {loading ? loadingText : children}
          </span>
          
          {rightIcon && !loading && (
            <span className="flex-shrink-0">{rightIcon}</span>
          )}
        </span>
      </motion.button>

      {/* Tooltip */}
      {tooltip && showTooltip && (
        <motion.div
          className={cn(
            'absolute z-50 px-2 py-1 text-sm text-white bg-gray-900 rounded shadow-lg pointer-events-none',
            {
              'bottom-full left-1/2 transform -translate-x-1/2 mb-2': tooltipPosition === 'top',
              'top-full left-1/2 transform -translate-x-1/2 mt-2': tooltipPosition === 'bottom',
              'right-full top-1/2 transform -translate-y-1/2 mr-2': tooltipPosition === 'left',
              'left-full top-1/2 transform -translate-y-1/2 ml-2': tooltipPosition === 'right',
            }
          )}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          transition={{ duration: 0.2 }}
        >
          {tooltip}
        </motion.div>
      )}
    </div>
  );
});

EnhancedButton.displayName = 'EnhancedButton';
