'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import Particles from './particles';
import { ScrollProgress } from './scroll-progress';

interface VisualEffectsSafeProps {
  className?: string;
  showParticles?: boolean;
  showScrollProgress?: boolean;
  showFloatingElements?: boolean;
  particleColor?: string;
  particleQuantity?: number;
}

// Componente que só renderiza após hidratação para evitar problemas de SSR
export function VisualEffectsSafe({
  className = '',
  showParticles = true,
  showScrollProgress = true,
  showFloatingElements = true,
  particleColor = '#3B82F6',
  particleQuantity = 50
}: Readonly<VisualEffectsSafeProps>) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Não renderizar nada no servidor
  if (!isClient) {
    return null;
  }

  return (
    <div className={cn("fixed inset-0 pointer-events-none z-0", className)}>
      {/* Scroll Progress */}
      {showScrollProgress && <ScrollProgress />}

      {/* Particles */}
      {showParticles && (
        <Particles
          className="absolute inset-0"
          quantity={particleQuantity}
          ease={80}
          color={particleColor}
          refresh
        />
      )}

      {/* Elementos flutuantes simples */}
      {showFloatingElements && (
        <div className="absolute inset-0 overflow-hidden">
          {/* Gradientes de fundo */}
          <motion.div
            className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-400/5 rounded-full blur-3xl"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.5, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />

          <motion.div
            className="absolute top-3/4 right-1/4 w-80 h-80 bg-purple-400/5 rounded-full blur-3xl"
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.2, 0.4, 0.2],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2,
            }}
          />

          <motion.div
            className="absolute top-1/2 left-1/2 w-64 h-64 bg-green-400/5 rounded-full blur-3xl"
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.1, 0.3, 0.1],
              x: [-50, 50, -50],
              y: [-30, 30, -30],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4,
            }}
          />

          {/* Elementos geométricos fixos */}
          <motion.div
            className="absolute top-[20%] left-[15%] w-4 h-4 bg-blue-400/10 rounded-full"
            animate={{
              y: [0, -30, 0],
              opacity: [0.1, 0.3, 0.1],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />

          <motion.div
            className="absolute top-[60%] right-[20%] w-3 h-3 bg-purple-400/10 rounded-sm"
            animate={{
              rotate: [0, 360],
              opacity: [0.1, 0.2, 0.1],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "linear",
              delay: 1,
            }}
          />

          <motion.div
            className="absolute top-[40%] left-[70%] w-0 h-0 border-l-2 border-r-2 border-b-4 border-l-transparent border-r-transparent border-b-pink-400/10"
            animate={{
              rotate: [0, 180, 360],
              y: [0, -25, 0],
              opacity: [0.1, 0.3, 0.1],
            }}
            transition={{
              duration: 7,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2,
            }}
          />

          <motion.div
            className="absolute top-[80%] left-[30%] w-4 h-4 bg-green-400/10 rounded-full"
            animate={{
              x: [0, 20, 0],
              opacity: [0.1, 0.3, 0.1],
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 9,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 3,
            }}
          />

          <motion.div
            className="absolute top-[10%] right-[40%] w-3 h-3 bg-yellow-400/10 rounded-sm"
            animate={{
              rotate: [0, -180, -360],
              scale: [1, 1.2, 1],
              opacity: [0.1, 0.2, 0.1],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "linear",
              delay: 4,
            }}
          />

          {/* Pontos de luz fixos */}
          <motion.div
            className="absolute top-[25%] left-[12%] w-1 h-1 bg-white rounded-full"
            animate={{
              opacity: [0, 1, 0],
              scale: [0, 1, 0],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />

          <motion.div
            className="absolute top-[45%] right-[12%] w-1 h-1 bg-white rounded-full"
            animate={{
              opacity: [0, 1, 0],
              scale: [0, 1, 0],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1,
            }}
          />

          <motion.div
            className="absolute top-[70%] left-[55%] w-1 h-1 bg-white rounded-full"
            animate={{
              opacity: [0, 1, 0],
              scale: [0, 1, 0],
            }}
            transition={{
              duration: 2.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2,
            }}
          />

          <motion.div
            className="absolute top-[15%] left-[78%] w-1 h-1 bg-white rounded-full"
            animate={{
              opacity: [0, 1, 0],
              scale: [0, 1, 0],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 3,
            }}
          />

          <motion.div
            className="absolute top-[85%] right-[42%] w-1 h-1 bg-white rounded-full"
            animate={{
              opacity: [0, 1, 0],
              scale: [0, 1, 0],
            }}
            transition={{
              duration: 3.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4,
            }}
          />
        </div>
      )}

      {/* Ondas de luz */}
      <motion.div
        className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-blue-100/10 to-transparent dark:from-blue-900/10"
        animate={{
          opacity: [0.1, 0.3, 0.1],
          scaleY: [1, 1.2, 1],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      {/* Raios de luz diagonais */}
      <motion.div
        className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-yellow-200/5 to-transparent dark:from-yellow-400/5"
        animate={{
          opacity: [0.1, 0.2, 0.1],
          rotate: [0, 5, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
    </div>
  );
}
